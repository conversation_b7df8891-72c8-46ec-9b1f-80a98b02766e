import 'package:flutter/material.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/level_config1.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/nsl_heirarchy_model1.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/nsl_tree_side_details_new.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/metric_info_model.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';


class ModernSidePanel1 extends StatefulWidget {
  final VoidCallback onClose;
  final double? width;
  final NSLHierarchyData1? nodeData;
  final NslTreeSidePanel? nodeDetails; // API node details
  final bool? isLoadingNodeDetails; // Loading state for node details
  final MetricsInfo? nodeTransactions; // API transactions data
  final bool? isLoadingTransactions; // Loading state for transactions
  final Map<String, dynamic>? dateRange;
  final Function(String)? onArrowTap; // Add arrow tap callback
  final VoidCallback?
      onGoLoPanelClosed; // Add callback for when GO/LO panel closes
  final int? clearSelectionTrigger;
  final Function(DateTime from, DateTime to)? onDateRangeChanged; // Add date change callback
  const ModernSidePanel1({
    super.key,
    required this.onClose,
    this.width,
    this.nodeData,
    this.nodeDetails, // Add to constructor
    this.isLoadingNodeDetails, // Add to constructor
    this.nodeTransactions, // Add transactions data
    this.isLoadingTransactions, // Add loading state for transactions
    this.dateRange,
    this.onArrowTap, // Add to constructor
    this.onGoLoPanelClosed, // Add to constructor
    this.clearSelectionTrigger,
    this.onDateRangeChanged, // Add date change callback
  });

  @override
  State<ModernSidePanel1> createState() => _ModernSidePanel1State();
}

class _ModernSidePanel1State extends State<ModernSidePanel1>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _selectedTabIndex = 0;
  String?
      _selectedMetricType; // Track which arrow was clicked for blue background
  DateTime? _selectedFromDate; // Track selected from date
  DateTime? _selectedToDate; // Track selected to date

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _selectedTabIndex = _tabController.index;
      });
    });
  }

  @override
  void didUpdateWidget(ModernSidePanel1 oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Clear selection when clearSelectionTrigger changes (indicating panel was closed and reopened)
    if (widget.clearSelectionTrigger != oldWidget.clearSelectionTrigger) {
      setState(() {
        _selectedMetricType = null;
      });
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final panelWidth = widget.width ?? _getResponsiveWidth(screenWidth);

    return LayoutBuilder(builder: (context, constraints) {
      return Stack(
        clipBehavior: Clip.none, // Allow content to extend beyond Stack bounds
        children: [
          // Main panel content
          Container(
            width: constraints.maxWidth,
            //panelWidth,
            height: MediaQuery.of(context).size.height,
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                left: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
            ),
            child: Column(
              children: [
                //  _buildCloseButton(),
                _buildHeader(),
                // _buildMetricsSection(),
                _buildTabNavigation(),
                Expanded(
                  child: _buildTabContent(),
                ),
              ],
            ),
          ),

          // Close icon positioned on the left border
          Positioned(
            left: -10, // Half outside the panel to sit on the border
            top: 24, // Adjust vertical position as needed
            child: MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                // Changed from InkWell
                onTap: widget.onClose,

                behavior: HitTestBehavior.opaque,
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.grey.shade400, width: 1),
                  ),
                  child: Icon(
                    Icons.close,
                    size: 12,
                    color: Colors.black,
                  ),
                ),
              ),
            ),
          ),
        ],
      );
    });
  }

  double _getResponsiveWidth(double screenWidth) {
    return screenWidth * 0.48;
  }

  Widget _buildCloseButton() {
    return InkWell(
      onTap: widget.onClose,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
        child: Text(
          'Close',
          style: TextStyle(
            fontSize: 10,
            color: Colors.black,
            fontFamily: 'TiemposText',
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return LayoutBuilder(builder: (context, constraints) {
      return Container(
        padding: EdgeInsets.all(AppSpacing.lg),
        constraints: BoxConstraints(maxWidth: constraints.maxWidth),
        decoration: BoxDecoration(
          color: Color(0XFFF1F3F4),
          border: Border(
            bottom: BorderSide(color: Colors.grey.shade200, width: 1),
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Left Column: M4, CEO Operations, Date toggles
            Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // M4 badge
                  // InkWell(
                  //   onTap: widget.onClose,
                  //   child: Icon(Icons.close, size: 24, color: Colors.black),
                  // ),
                  //  SizedBox(height: AppSpacing.size6),
                  Container(
                    height: 48,
                    width: 48,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 12,
                    ),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white,
                    ),
                    child: Text(
                      widget.nodeData!.level,
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s16,
                        fontWeight: FontManager.bold,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  SizedBox(height: AppSpacing.size6),

                  // Dynamic title from JSON
                  Text(
                    widget.nodeData!.title,
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s14,
                      fontWeight: FontManager.bold,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                  SizedBox(height: AppSpacing.size10),

                  // Date toggle buttons
                  Row(
                    children: [
                      InkWell(
                        onTap: () => _selectFromDate(context),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            border: Border.all(
                              color: Color(0XFFB4B4B4),
                            ),
                            //  borderRadius: BorderRadius.circular(4),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.calendar_today,
                                  size: 10, color: Colors.black),
                              SizedBox(width: 4),
                              Text(
                                _formatDate(
                                    _selectedFromDate?.toIso8601String() ??
                                        widget.dateRange?['from']),
                                style: FontManager.getCustomStyle(
                                  fontSize: FontManager.s10,
                                  fontWeight: FontManager.regular,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.black,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(width: AppSpacing.xxs),
                      Text(
                        'To',
                        style: FontManager.getCustomStyle(
                          fontSize: FontManager.s10,
                          fontWeight: FontManager.regular,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                      SizedBox(width: AppSpacing.xxs),
                      // SizedBox(width: AppSpacing.xs),
                      InkWell(
                        onTap: () => _selectToDate(context),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            border: Border.all(
                              color: Color(0XFFB4B4B4),
                            ),
                            //borderRadius: BorderRadius.circular(4),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.calendar_today,
                                  size: 10, color: Colors.black),
                              SizedBox(width: 4),
                              Text(
                                _formatDate(
                                    _selectedToDate?.toIso8601String() ??
                                        widget.dateRange?['to']),
                                style: FontManager.getCustomStyle(
                                  fontSize: FontManager.s10,
                                  fontWeight: FontManager.regular,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.black,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // SizedBox(width: AppSpacing.lg),

            // Right Column: Dynamic Metrics table
            Expanded(
              flex: 2,
              child: _buildDynamicMetricsTable(),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildMetricsSection() {
    return Container(
      padding: EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200, width: 1),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  'M3 Nodes',
                  '6',
                  Colors.blue,
                ),
              ),
              SizedBox(width: AppSpacing.md),
              Expanded(
                child: _buildMetricCard(
                  'Total Transactions',
                  '02345.5K',
                  Colors.green,
                ),
              ),
            ],
          ),
          SizedBox(height: AppSpacing.md),
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  'Total GOs',
                  '45',
                  Colors.orange,
                ),
              ),
              SizedBox(width: AppSpacing.md),
              Expanded(
                child: _buildMetricCard(
                  'Total LOs',
                  '234',
                  Colors.purple,
                ),
              ),
            ],
          ),
          SizedBox(height: AppSpacing.md),
          Row(
            children: [
              Expanded(
                child: _buildSummaryIndicator(
                  'Revenue',
                  '\$76.M',
                  'Cost',
                  '\$1.33.M',
                  'Margin',
                  '39.3%',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMetricCard(String label, String value, Color color) {
    return Container(
      padding: EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s12,
              fontWeight: FontManager.regular,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey.shade600,
            ),
          ),
          SizedBox(height: AppSpacing.xs),
          Text(
            value,
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s16,
              fontWeight: FontManager.bold,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryIndicator(
    String label1,
    String value1,
    String label2,
    String value2,
    String label3,
    String value3,
  ) {
    return Container(
      padding: EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildSummaryItem(label1, value1),
          _buildSummaryItem(label2, value2),
          _buildSummaryItem(label3, value3),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value) {
    return Column(
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s10,
            fontWeight: FontManager.regular,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.grey.shade600,
          ),
        ),
        SizedBox(height: AppSpacing.xs),
        Text(
          value,
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s12,
            fontWeight: FontManager.semiBold,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildTabNavigation() {
    return Container(
      padding: EdgeInsets.only(
        left: 7,
      ),
      decoration: BoxDecoration(
        color: Color(0XFFE4EDFF),
        border: Border(
          bottom: BorderSide(
            color: Color(0XFFE4EDFF),
          ),
        ),
      ),
      child: TabBar(
        tabAlignment: TabAlignment.start,
        controller: _tabController,
        isScrollable: true,
        dividerColor: Colors.transparent,
        // indicatorColor: Colors.transparent,
        labelPadding: EdgeInsets.symmetric(horizontal: 16.0),

        // labelColor: Colors.blue.shade700,
        //unselectedLabelColor: Colors.grey.shade600,
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(color: Color(0XFF0058FF), width: 2.5),
          insets:
              EdgeInsets.only(bottom: 15), // Positions indicator closer to text
        ),
        //  indicatorColor: Colors.blue.shade700,
        //  indicatorWeight: 2,
        labelStyle: FontManager.getCustomStyle(
            fontSize: FontManager.s11,
            fontWeight: FontManager.bold,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black),
        unselectedLabelStyle: FontManager.getCustomStyle(
            fontSize: FontManager.s11,
            fontWeight: FontManager.regular,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black),
        tabs: const [
          Tab(text: 'Standalone'),
          Tab(text: 'Consolidated'),
          Tab(text: 'BET Breakdown'),
          //  Tab(text: 'Trends'),
        ],
      ),
    );
  }

  Widget _buildTabContent() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildStandaloneTab(),
        _buildConsolidatedTab(),
        _buildBetBreakdownTab(),
        // _buildTrendsTab(),
      ],
    );
  }

  Widget _buildStandaloneTab() {
    return LayoutBuilder(builder: (context, constraints) {
      return SingleChildScrollView(
        //  padding: EdgeInsets.only(top:AppSpacing.md, left:AppSpacing.lg,right:AppSpacing.lg),
        child: ConstrainedBox(
          constraints: BoxConstraints(minWidth: constraints.maxWidth),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFinancialCards(),
              SizedBox(height: AppSpacing.lg),
              _buildIncomeStatement(),
              SizedBox(height: AppSpacing.lg),
              _buildBalanceSheet(),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildFinancialCards() {
    final financialData =
        widget.nodeData?.financialDataByNode?.standalone?.summary;

    return LayoutBuilder(builder: (context, constraints) {
      return Padding(
        padding: EdgeInsets.only(
            top: AppSpacing.md, left: AppSpacing.lg, right: AppSpacing.lg),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: _buildFinancialCard(
                    'Total Revenue',
                    financialData?.totalRevenue['value'] ?? '',
                    financialData?.totalRevenue['change'] ?? '',
                    Colors.green,
                    Icons.trending_up,
                  ),
                ),
                SizedBox(width: AppSpacing.md),
                Expanded(
                  child: _buildFinancialCard(
                    'Net Margin',
                    financialData?.netMargin['value'] ?? '',
                    financialData?.netMargin['change'] ?? '',
                    Colors.blue,
                    Icons.trending_up,
                  ),
                ),
              ],
            ),
            SizedBox(height: AppSpacing.md),
            Row(
              children: [
                Expanded(
                  child: _buildFinancialCard(
                    'Total Transactions',
                    financialData?.totalTransactions['value'] ?? '',
                    financialData?.totalTransactions['change'] ?? '',
                    Colors.orange,
                    Icons.trending_up,
                  ),
                ),
                SizedBox(width: AppSpacing.md),
                Expanded(
                  child: _buildFinancialCard(
                    'BET Efficiency',
                    financialData?.betEfficiency['value'] ?? '',
                    financialData?.betEfficiency['change'] ?? '',
                    Colors.purple,
                    Icons.trending_up,
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }
Widget _buildFinancialCard(
  String title,
  String value,
  String trend,
  Color color,
  IconData icon,
) {
  // Get data from API if available
  String displayValue = 'N/A';
  String displayTrend = trend;
  
  if (widget.nodeTransactions?.result != null) {
    final transactionData = widget.nodeTransactions!.result!;
    
    switch (title) {
      case 'Total Revenue':
        if (transactionData.revenue != null) {
          displayValue = '\$${_formatNumber(transactionData.revenue!)}';
        }
        break;
      case 'Net Margin':
        if (transactionData.margin != null) {
          displayValue = '${transactionData.margin!.toStringAsFixed(1)}%';
        }
        break;
      case 'Total Transactions':
        if (transactionData.totalTransactions != null) {
          displayValue = _formatTransactionCount(transactionData.totalTransactions!);
        }
        break;
      // BET Efficiency will use the default value passed in
    }
  }
  
  // Show loading indicator if data is being loaded
  if (widget.isLoadingTransactions == true && 
      title != 'BET Efficiency') { // Skip loading for BET Efficiency
    return Container(
      padding: EdgeInsets.all(AppSpacing.size10),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Color(0XFFB4B4B4)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: FontManager.getCustomStyle(
                  fontSize: FontManager.s14,
                  fontWeight: FontManager.regular,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              SizedBox(
                width: 12,
                height: 12,
                child: CircularProgressIndicator(
                  strokeWidth: 1.5,
                  valueColor: AlwaysStoppedAnimation<Color>(color),
                ),
              ),
            ],
          ),
          SizedBox(height: AppSpacing.sm),
          Text(
            displayTrend,
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s10,
              fontWeight: FontManager.regular,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  // Use the API value if available, otherwise fall back to the passed value
  final finalValue = displayValue != 'N/A' ? displayValue : value;
  
  return Container(
    padding: EdgeInsets.all(AppSpacing.size10),
    decoration: BoxDecoration(
      color: Colors.white,
      border: Border.all(color: Color(0XFFB4B4B4)),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s14,
                fontWeight: FontManager.regular,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
            Text(
              finalValue,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s13,
                fontWeight: FontManager.bold,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ],
        ),
        SizedBox(height: AppSpacing.sm),
        Text(
          displayTrend,
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s10,
            fontWeight: FontManager.regular,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    ),
  );
}

// Helper methods (already in your code, included for completeness)
String _formatNumber(num value) {
  if (value >= 1000000) {
    return '${(value / 1000000).toStringAsFixed(1)}M';
  } else if (value >= 1000) {
    return '${(value / 1000).toStringAsFixed(1)}K';
  } else {
    return value.toStringAsFixed(0);
  }
}

String _formatTransactionCount(int value) {
  if (value >= 1000000) {
    return '${(value / 1000000).toStringAsFixed(1)}M';
  } else if (value >= 1000) {
    return '${(value / 1000).toStringAsFixed(1)}K';
  } else {
    return value.toString();
  }
}

  Widget _buildIncomeStatement() {
    return Container(
      margin: EdgeInsets.only(left: 8, right: 8),
      padding: EdgeInsets.symmetric(
        horizontal: AppSpacing.md,
        vertical: AppSpacing.sm,
      ),
      decoration: BoxDecoration(
        color: Color(0xFFF8F9FA),

        // borderRadius: BorderRadius.circular(8),
        // border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Income Statement - Standalone',
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s14,
              fontWeight: FontManager.semiBold,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
         // SizedBox(height: AppSpacing.size6),
          // Text(
          //   // 'For the Month Ended November 30, 2024',
          //   // style: FontManager.getCustomStyle(
          //   //   fontSize: FontManager.s10,
          //   //   fontWeight: FontManager.regular,
          //   //   fontFamily: FontManager.fontFamilyTiemposText,
          //   //   color: Colors.black,
          //   // ),
          // ),
         // SizedBox(height: 2),
          Divider(color: Color(0xFFB4B4B4)),
          SizedBox(height: AppSpacing.size6),
          Column(
            children: [
              _buildIncomeStatementHeader(),
              ..._buildIncomeStatementRows(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildIncomeStatementHeader() {
    return Container(
      padding: EdgeInsets.only(
          left: AppSpacing.sm,
          bottom: AppSpacing.xxs,
          top: AppSpacing.sm,
          right: AppSpacing.sm),
      decoration: BoxDecoration(

          //  color: Colors.grey.shade50,
          // borderRadius: const BorderRadius.only(
          //   topLeft: Radius.circular(8),
          //   topRight: Radius.circular(8),
          // ),
          ),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              'Line Item',
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontManager.semiBold,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'Amount',
              textAlign: TextAlign.right,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontManager.semiBold,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              '% of Revenue',
              textAlign: TextAlign.right,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontManager.semiBold,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildIncomeStatementRows() {
    // Show loading indicator if transactions are loading
    if (widget.isLoadingTransactions == true) {
      return [
        Container(
          padding: EdgeInsets.all(AppSpacing.lg),
          child: Center(
            child: CircularProgressIndicator(),
          ),
        ),
      ];
    }

    // Check if we have PnL data from API
    if (widget.nodeTransactions?.result?.pnl == null || 
        widget.nodeTransactions!.result!.pnl!.isEmpty) {
      return [
        Container(
          padding: EdgeInsets.all(AppSpacing.lg),
          child: Text(
            'No PnL data available',
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s12,
              fontWeight: FontManager.regular,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey.shade600,
            ),
          ),
        ),
      ];
    }

    final pnlData = widget.nodeTransactions!.result!.pnl!;
    
    List<Widget> rows = [];
    
    // Find Income and Expense sections
    BalanceSheet? incomeSection;
    BalanceSheet? expenseSection;
    
    for (var section in pnlData) {
      if (section.name?.toLowerCase().contains('income') == true) {
        incomeSection = section;
      } else if (section.name?.toLowerCase().contains('expense') == true) {
        expenseSection = section;
      }
    }
    
    double totalIncome = 0.0;
    double totalExpense = 0.0;
    
    // Build Income section
    if (incomeSection != null) {
      totalIncome = incomeSection.sum ?? 0.0;
      
      // Income header (optional, can be removed if not needed)
      rows.add(_buildIncomeStatementRow(
        'Income',
        '',
        '',
        isSubHeader: true,
      ));
      
      // Individual income items
      if (incomeSection.details != null) {
        for (var detail in incomeSection.details!) {
          final amount = detail.value ?? 0.0;
          final percentage = totalIncome > 0 ? (amount / totalIncome) * 100 : 0.0;
          
          rows.add(_buildIncomeStatementRow(
            detail.name ?? '',
            _formatCurrency(amount),
            '${percentage.toStringAsFixed(1)}%',
          ));
        }
      }
      
      // Total Income
      rows.add(_buildIncomeStatementRow(
        'Total Income',
        _formatCurrency(totalIncome),
        '100%', // Show 100% for totals
        isTotal: true,
      ));
    }
    
    // Build Expense section
    if (expenseSection != null) {
      totalExpense = expenseSection.sum ?? 0.0;
      
      // Expense header (optional, can be removed if not needed)
      rows.add(_buildIncomeStatementRow(
        'Expense',
        '',
        '',
        isSubHeader: true,
      ));
      
      // Individual expense items
      if (expenseSection.details != null) {
        for (var detail in expenseSection.details!) {
          final amount = detail.value ?? 0.0;
          final percentage = totalExpense > 0 ? (amount / totalExpense) * 100 : 0.0;
          
          rows.add(_buildIncomeStatementRow(
            detail.name ?? '',
            _formatCurrency(amount),
            '${percentage.toStringAsFixed(1)}%',
          ));
        }
      }
      
      // Total Expense
      rows.add(_buildIncomeStatementRow(
        'Total Expense',
        _formatCurrency(totalExpense),
        '100%', // Show 100% for totals
        isTotal: true,
      ));
    }
    
    // Net Profit calculation
    final netProfit = totalIncome - totalExpense;
    rows.add(_buildIncomeStatementRow(
      'Net Profit',
      _formatCurrency(netProfit),
      '',
      isFinal: true,
    ));
    
    return rows;
  }

  Widget _buildIncomeStatementRow(String item, String amount, String percentage,
      {bool isTotal = false, bool isFinal = false, bool isSubHeader = false}) {
    final fontWeight = (isTotal || isFinal || isSubHeader) 
        ? FontManager.bold 
        : FontManager.regular;
    final backgroundColor = isTotal
        ? Color(0xFFF0F0F0)
        : (isFinal ? Color(0xFFE8F5E8) : Colors.transparent);

    return Container(
      padding: EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        color: backgroundColor,
      ),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              item,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: fontWeight,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              amount,
              textAlign: TextAlign.right,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: fontWeight,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              percentage,
              textAlign: TextAlign.right,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: fontWeight,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to format currency values
  String _formatCurrency(double value) {
    if (value >= 1000000) {
      return '\$${(value / 1000000).toStringAsFixed(1)}M';
    } else if (value >= 1000) {
      return '\$${(value / 1000).toStringAsFixed(1)}K';
    } else {
      return '\$${value.toStringAsFixed(0)}';
    }
  }

  Widget _buildBalanceSheet() {
    return Container(
      margin: EdgeInsets.only(left: 8, right: 8),
      padding: EdgeInsets.symmetric(
        horizontal: AppSpacing.md,
        vertical: AppSpacing.sm,
      ),
      decoration: BoxDecoration(
        color: Color(0xFFF8F9FA),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Balance Sheet - Standalone',
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s14,
              fontWeight: FontManager.semiBold,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
         // SizedBox(height: AppSpacing.size6),
          // Text(
          //   'As of November 30, 2024',
          //   style: FontManager.getCustomStyle(
          //     fontSize: FontManager.s10,
          //     fontWeight: FontManager.regular,
          //     fontFamily: FontManager.fontFamilyTiemposText,
          //     color: Colors.black,
          //   ),
          // ),
          // SizedBox(height: 2),
          Divider(color: Color(0xFFB4B4B4)),
          SizedBox(height: AppSpacing.size6),
          Column(
            children: [
              _buildBalanceSheetHeader(),
              ..._buildBalanceSheetRows(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceSheetHeader() {
    return Container(
      padding: EdgeInsets.only(
          left: AppSpacing.sm,
          bottom: AppSpacing.xxs,
          top: AppSpacing.sm,
          right: AppSpacing.sm),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              'Line Item',
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontManager.semiBold,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'Amount',
              textAlign: TextAlign.right,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontManager.semiBold,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              '% of Revenue',
              textAlign: TextAlign.right,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontManager.semiBold,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildBalanceSheetRows() {
    // Show loading indicator if transactions are loading
    if (widget.isLoadingTransactions == true) {
      return [
        Container(
          padding: EdgeInsets.all(AppSpacing.lg),
          child: Center(
            child: CircularProgressIndicator(),
          ),
        ),
      ];
    }

    // Check if we have Balance Sheet data from API
    if (widget.nodeTransactions?.result?.balanceSheet == null || 
        widget.nodeTransactions!.result!.balanceSheet!.isEmpty) {
      return [
        Container(
          padding: EdgeInsets.all(AppSpacing.lg),
          child: Text(
            'No Balance Sheet data available',
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s12,
              fontWeight: FontManager.regular,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey.shade600,
            ),
          ),
        ),
      ];
    }

    final balanceSheetData = widget.nodeTransactions!.result!.balanceSheet!;
    
    List<Widget> rows = [];
    
    // Find Asset and Liability sections
    BalanceSheet? assetSection;
    BalanceSheet? liabilitySection;
    
    for (var section in balanceSheetData) {
      if (section.name?.toLowerCase().contains('asset') == true) {
        assetSection = section;
      } else if (section.name?.toLowerCase().contains('liability') == true) {
        liabilitySection = section;
      }
    }
    
    // Build Asset section
    if (assetSection != null) {
      final assetTotal = assetSection.sum ?? 0.0;
      
      // Asset header
      rows.add(_buildBalanceSheetRow(
        'Asset',
        '',
        '',
        isSubHeader: true,
      ));
      
      // Individual asset items
      if (assetSection.details != null) {
        for (var detail in assetSection.details!) {
          final amount = detail.value ?? 0.0;
          final percentage = assetTotal > 0 ? (amount / assetTotal) * 100 : 0.0;
          
          rows.add(_buildBalanceSheetRow(
            detail.name ?? '',
            _formatCurrency(amount),
            '${percentage.toStringAsFixed(1)}%',
          ));
        }
      }
      
      // Asset Total
      rows.add(_buildBalanceSheetRow(
        'Asset Total',
        _formatCurrency(assetTotal),
        '100%', // No percentage for totals as per your note
        isTotal: true,
      ));
    }
    
    // Build Liability section
    if (liabilitySection != null) {
      final liabilityTotal = liabilitySection.sum ?? 0.0;
      
      // Liability header
      rows.add(_buildBalanceSheetRow(
        'Liability',
        '',
        '',
        isSubHeader: true,
      ));
      
      // Individual liability items
      if (liabilitySection.details != null) {
        for (var detail in liabilitySection.details!) {
          final amount = detail.value ?? 0.0;
          final percentage = liabilityTotal > 0 ? (amount / liabilityTotal) * 100 : 0.0;
          
          rows.add(_buildBalanceSheetRow(
            detail.name ?? '',
            _formatCurrency(amount),
            '${percentage.toStringAsFixed(1)}%',
          ));
        }
      }
      
      // Liability Total
      rows.add(_buildBalanceSheetRow(
        'Liability Total',
        _formatCurrency(liabilityTotal),
        '100%', // No percentage for totals as per your note
        isTotal: true,
      ));
    }
    
    return rows;
  }

  Widget _buildBalanceSheetRow(String item, String amount, String percentage,
      {bool isTotal = false, bool isFinal = false, bool isSubHeader = false}) {
    final fontWeight = (isTotal || isFinal || isSubHeader) 
        ? FontManager.bold 
        : FontManager.regular;
    final backgroundColor = isTotal
        ? Color(0xFFF0F0F0)
        : (isFinal ? Color(0xFFE8F5E8) : Colors.transparent);

    return Container(
      padding: EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        color: backgroundColor,
      ),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              item,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: fontWeight,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              amount,
              textAlign: TextAlign.right,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: fontWeight,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              percentage,
              textAlign: TextAlign.right,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: fontWeight,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConsolidatedTab() {
    final consolidatedData =
        widget.nodeData?.financialDataByNode?.consolidated?.summary ?? [];
    final pairedData = [];
    for (var i = 0; i < consolidatedData.length; i += 2) {
      if (i + 1 < consolidatedData.length) {
        pairedData.add([consolidatedData[i], consolidatedData[i + 1]]);
      } else {
        pairedData.add([consolidatedData[i]]); // Last item if odd count
      }
    }
    return SingleChildScrollView(
      padding: EdgeInsets.only(
          top: AppSpacing.md, left: AppSpacing.lg, right: AppSpacing.lg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ...pairedData.map(
            (pair) => Row(
              children: [
                // First item in pair
                Expanded(
                  child: _buildMetricCardConsolidated(pair[0]),
                ),
                SizedBox(width: AppSpacing.sm), // Spacing between cards
                // Second item in pair (if exists)
                pair.length > 1
                    ? Expanded(child: _buildMetricCardConsolidated(pair[1]))
                    : Expanded(
                        child: Container()), // Empty container if odd count
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _buildMetricCardConsolidated(Map<String, dynamic> item) => Container(
        margin: EdgeInsets.only(bottom: AppSpacing.sm),
        padding: EdgeInsets.all(AppSpacing.size10),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: Color(0XFFB4B4B4)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item['title'] ?? 'N/A',
                  style: FontManager.getCustomStyle(
                    fontSize: FontManager.s14,
                    fontWeight: FontManager.regular,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                ),
                SizedBox(height: AppSpacing.xs),
                Text(
                  item['change'] ?? '',
                  style: FontManager.getCustomStyle(
                    fontSize: FontManager.s10,
                    fontWeight: FontManager.regular,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
            Text(item['value'] ?? 'N/A',
                style: FontManager.getCustomStyle(
                  fontSize: FontManager.s13,
                  fontWeight: FontManager.bold,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                )),
          ],
        ),
      );
  Widget _buildBetBreakdownTab() {
    final betBreakdown =
        widget.nodeData?.financialDataByNode?.betBreakdown ?? [];

    return SingleChildScrollView(
      padding: EdgeInsets.only(
          top: AppSpacing.md, left: AppSpacing.lg, right: AppSpacing.lg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // BET breakdown items
          ...betBreakdown
              .map((bet) => Container(
                    margin: EdgeInsets.only(bottom: AppSpacing.md),
                    padding: EdgeInsets.all(AppSpacing.md),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(color: Color(0XFFB4B4B4)),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // BET header
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    bet.name,
                                    style: FontManager.getCustomStyle(
                                      fontSize: FontManager.s14,
                                      fontWeight: FontManager.bold,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                                  SizedBox(height: AppSpacing.xs),
                                  Text(
                                    bet.type,
                                    style: FontManager.getCustomStyle(
                                      fontSize: FontManager.s12,
                                      fontWeight: FontManager.regular,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.grey.shade600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Text(
                              bet.cost,
                              style: FontManager.getCustomStyle(
                                fontSize: FontManager.s16,
                                fontWeight: FontManager.bold,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.black,
                              ),
                            ),
                          ],
                        ),

                        SizedBox(height: AppSpacing.md),

                        // BET details
                        Container(
                          padding: EdgeInsets.all(AppSpacing.sm),
                          decoration: BoxDecoration(
                            color: Color(0xFFF8F9FA),
                          ),
                          child: Column(
                            children: bet.details.entries
                                .map(
                                  (entry) => Padding(
                                    padding: EdgeInsets.symmetric(
                                        vertical: AppSpacing.xs),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          entry.key,
                                          style: FontManager.getCustomStyle(
                                            fontSize: FontManager.s12,
                                            fontWeight: FontManager.regular,
                                            fontFamily: FontManager
                                                .fontFamilyTiemposText,
                                            color: Colors.grey.shade700,
                                          ),
                                        ),
                                        Text(
                                          entry.value.toString(),
                                          style: FontManager.getCustomStyle(
                                            fontSize: FontManager.s12,
                                            fontWeight: FontManager.semiBold,
                                            fontFamily: FontManager
                                                .fontFamilyTiemposText,
                                            color: Colors.black,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                )
                                .toList(),
                          ),
                        ),
                      ],
                    ),
                  ))
              .toList(),
        ],
      ),
    );
  }

  Widget _buildTrendsTab() {
    final trendsData =
        widget.nodeData?.financialDataByNode?.trends?.trends ?? [];

    return SingleChildScrollView(
      padding: EdgeInsets.only(
          top: AppSpacing.md, left: AppSpacing.lg, right: AppSpacing.lg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Trends cards - two per row
          ..._buildTrendsInRows(trendsData),
        ],
      ),
    );
  }

  Widget _buildTrendCard(
      String title, String value, String description, bool isPositive) {
    final color = isPositive ? Colors.green : Colors.red;
    final icon = isPositive ? Icons.trending_up : Icons.trending_down;

    return Container(
      padding: EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Color(0XFFB4B4B4)),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          SizedBox(width: AppSpacing.md),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: FontManager.getCustomStyle(
                    fontSize: FontManager.s14,
                    fontWeight: FontManager.semiBold,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                ),
                SizedBox(height: AppSpacing.xs),
                Text(
                  description,
                  style: FontManager.getCustomStyle(
                    fontSize: FontManager.s12,
                    fontWeight: FontManager.regular,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          Text(
            value,
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s16,
              fontWeight: FontManager.bold,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Color _getTrendColor(String value) {
    if (value.startsWith('+')) {
      return Colors.green;
    } else if (value.startsWith('-')) {
      return Colors.red;
    }
    return Colors.grey;
  }

  IconData _getTrendIcon(String value) {
    if (value.startsWith('+')) {
      return Icons.trending_up;
    } else if (value.startsWith('-')) {
      return Icons.trending_down;
    }
    return Icons.trending_flat;
  }

  // Dynamic metrics table builder
  Widget _buildDynamicMetricsTable() {
    final level = widget.nodeData?.level ?? 'M4';
    final config = LevelConfigurations.getConfigOrDefault(level);
    final accessor = DynamicDataAccessor(widget.nodeData);

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Color(0XFFB4B4B4)),
      ),
      child: Column(
        children: config.metricsLayout.map<Widget>((layoutItem) {
          if (layoutItem is MetricRow) {
            return _buildDynamicMetricRow(layoutItem, accessor);
          } else if (layoutItem is FinancialRow) {
            return _buildDynamicFinancialRow(layoutItem, accessor);
          }
          return Container(); // Fallback
        }).toList(),
      ),
    );
  }

  Widget _buildDynamicMetricRow(
      MetricRow metricRow, DynamicDataAccessor accessor) {
    // Check if this cell is selected for blue background
    final isLeftSelected = _selectedMetricType == metricRow.left.label;
    final isRightSelected = _selectedMetricType == metricRow.right.label;

    return Row(
      children: [
        // Left cell with conditional blue background
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: isLeftSelected
                  ? Color(0xFF0058FF)
                  : Colors.white, // Blue when selected
              border: Border(
                right: BorderSide(color: Color(0XFFB4B4B4)),
                bottom: BorderSide(color: Color(0XFFB4B4B4)),
              ),
            ),
            child: InkWell(
              onTap: _shouldShowArrow(metricRow.left.label)
                  ? () => _onArrowTapped(metricRow.left.label)
                  : null,
              child: Container(
                padding: EdgeInsets.all(AppSpacing.size10),
                child: Row(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          metricRow.left.label,
                          style: FontManager.getCustomStyle(
                            fontSize: FontManager.s10,
                            fontWeight: FontManager.regular,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: isLeftSelected ? Colors.white : Colors.black,
                          ),
                        ),
                        SizedBox(width: 5),
                        if (_shouldShowArrow(metricRow.left.label))
                          Icon(
                            Icons.arrow_forward,
                            size: 12,
                            color: isLeftSelected ? Colors.white : Colors.black,
                          ),
                      ],
                    ),
                    Spacer(),
                    _buildLoadingOrValue(
                      metricRow.left.label,
                      accessor.formatValue(
                        accessor.getMetricValue(metricRow.left.dataPath),
                        metricRow.left.formatter,
                      ),
                      isLeftSelected ? Colors.white : Colors.black,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        // Right cell with conditional blue background
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: isRightSelected
                  ? Color(0xFF0058FF)
                  : Colors.white, // Blue when selected
              border: Border(
                bottom: BorderSide(color: Color(0XFFB4B4B4)),
              ),
            ),
            child: InkWell(
              onTap: _shouldShowArrow(metricRow.right.label)
                  ? () => _onArrowTapped(metricRow.right.label)
                  : null,
              child: Container(
                padding: EdgeInsets.all(AppSpacing.size10),
                child: Row(
                  children: [
                    Row(
                      children: [
                        Text(
                          metricRow.right.label,
                          style: FontManager.getCustomStyle(
                            fontSize: FontManager.s10,
                            fontWeight: FontManager.regular,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color:
                                isRightSelected ? Colors.white : Colors.black,
                          ),
                        ),
                        SizedBox(width: 5),
                        if (_shouldShowArrow(metricRow.right.label))
                          Icon(
                            Icons.arrow_forward,
                            size: 12,
                            color:
                                isRightSelected ? Colors.white : Colors.black,
                          ),
                      ],
                    ),
                    Spacer(),
                    _buildLoadingOrValue(
                      metricRow.right.label,
                      accessor.formatValue(
                        accessor.getMetricValue(metricRow.right.dataPath),
                        metricRow.right.formatter,
                      ),
                      isRightSelected ? Colors.white : Colors.black,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Helper method to show loading or value based on API data
  Widget _buildLoadingOrValue(String label, String defaultValue, Color textColor) {
    // Check if this is a GO or LO metric and we have API data
    if (label.toLowerCase().contains('gos') && widget.nodeDetails?.result?.goCount != null) {
      return Text(
        widget.nodeDetails!.result!.goCount.toString(),
        style: FontManager.getCustomStyle(
          fontSize: FontManager.s10,
          fontWeight: FontManager.bold,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: textColor,
        ),
      );
    } else if (label.toLowerCase().contains('los') && widget.nodeDetails?.result?.loCount != null) {
      return Text(
        widget.nodeDetails!.result!.loCount.toString(),
        style: FontManager.getCustomStyle(
          fontSize: FontManager.s10,
          fontWeight: FontManager.bold,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: textColor,
        ),
      );
    } 
    // Check for transactions API data
    else if (label.toLowerCase().contains('transaction') && widget.nodeTransactions?.result?.totalTransactions != null) {
      return Text(
        _formatTransactionCount(widget.nodeTransactions!.result!.totalTransactions!),
        style: FontManager.getCustomStyle(
          fontSize: FontManager.s10,
          fontWeight: FontManager.bold,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: textColor,
        ),
      );
    }
    // Check for loading states
    else if ((label.toLowerCase().contains('gos') || label.toLowerCase().contains('los')) && 
               widget.isLoadingNodeDetails == true) {
      // Show loading indicator for GO/LO metrics
      return SizedBox(
        width: 12,
        height: 12,
        child: CircularProgressIndicator(
          strokeWidth: 1.5,
          valueColor: AlwaysStoppedAnimation<Color>(textColor),
        ),
      );
    } else if (label.toLowerCase().contains('transaction') && widget.isLoadingTransactions == true) {
      // Show loading indicator for transactions
      return SizedBox(
        width: 12,
        height: 12,
        child: CircularProgressIndicator(
          strokeWidth: 1.5,
          valueColor: AlwaysStoppedAnimation<Color>(textColor),
        ),
      );
    } else {
      // Use default value for other metrics
      return Text(
        defaultValue,
        style: FontManager.getCustomStyle(
          fontSize: FontManager.s10,
          fontWeight: FontManager.bold,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: textColor,
        ),
      );
    }
  }

  // Handle arrow tap with state update
  void _onArrowTapped(String metricType) {
    setState(() {
      _selectedMetricType = metricType;
    });
    widget.onArrowTap?.call(metricType);
  }

  bool _shouldShowArrow(String label) {
    return label.toLowerCase().contains('go') ||
        label.toLowerCase().contains('lo');
  }

  Widget _buildDynamicFinancialRow(
      FinancialRow financialRow, DynamicDataAccessor accessor) {
    return Row(
      children: financialRow.cells.asMap().entries.map((entry) {
        final index = entry.key;
        final cell = entry.value;
        final isLast = index == financialRow.cells.length - 1;

        return Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: Color(0XFFE7F8F5),
              border: Border(
                right: isLast
                    ? BorderSide.none
                    : BorderSide(color: Color(0XFFB4B4B4)),
              ),
            ),
            padding: EdgeInsets.all(AppSpacing.size10),
            child: Row(
              children: [
                Text(
                  cell.label,
                  style: FontManager.getCustomStyle(
                    fontSize: FontManager.s8,
                    fontWeight: FontManager.regular,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                ),
                Spacer(),
                _buildFinancialValue(cell),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  // Helper method to show financial values from API or fallback
  Widget _buildFinancialValue(MetricCell cell) {
    // Show loading indicator if transactions are loading
    if (widget.isLoadingTransactions == true) {
      return SizedBox(
        width: 12,
        height: 12,
        child: CircularProgressIndicator(
          strokeWidth: 1.5,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
        ),
      );
    }
    
    String value = 'N/A';
    
    // Check if we have transactions API data
    if (widget.nodeTransactions?.result != null) {
      final transactionData = widget.nodeTransactions!.result!;
      
      switch (cell.dataPath) {
        case 'revenue':
          if (transactionData.revenue != null) {
            value = _formatNumber(transactionData.revenue!);
          }
          break;
        case 'cost':
          if (transactionData.cost != null) {
            value = _formatNumber(transactionData.cost!);
          }
          break;
        case 'margin':
          if (transactionData.margin != null) {
            value = '${transactionData.margin!.toStringAsFixed(1)}%';
          }
          break;
      }
    }
    
    // // If no API data, use accessor fallback
    // if (value == 'N/A') {
    //   final accessor = DynamicDataAccessor(widget.nodeData);
    //   value = accessor.formatValue(
    //     accessor.getFinancialValue(cell.dataPath),
    //     cell.formatter,
    //   );
    // }
    
    return Text(
      value,
      style: FontManager.getCustomStyle(
        fontSize: FontManager.s10,
        fontWeight: FontManager.bold,
        fontFamily: FontManager.fontFamilyTiemposText,
        color: Colors.black,
      ),
    );
  }

  // // Helper method to format numbers properly
  // String _formatNumber(num value) {
  //   if (value >= 1000000) {
  //     return '\$${(value / 1000000).toStringAsFixed(1)}M';
  //   } else if (value >= 1000) {
  //     return '\$${(value / 1000).toStringAsFixed(1)}K';
  //   } else {
  //     return '\$${value.toStringAsFixed(0)}';
  //   }
  // }

  // // Helper method to format transaction count
  // String _formatTransactionCount(int value) {
  //   if (value >= 1000000) {
  //     return '${(value / 1000000).toStringAsFixed(1)}M';
  //   } else if (value >= 1000) {
  //     return '${(value / 1000).toStringAsFixed(1)}K';
  //   } else {
  //     return value.toString();
  //   }
  // }

  // Helper method to build trends in rows of two
  List<Widget> _buildTrendsInRows(List<dynamic> trendsData) {
    final List<Widget> rows = [];

    for (int i = 0; i < trendsData.length; i += 2) {
      final trend1 = trendsData[i];
      final trend2 = i + 1 < trendsData.length ? trendsData[i + 1] : null;

      rows.add(
        Row(
          children: [
            Expanded(
              child: _buildSingleTrendCard(trend1),
            ),
            SizedBox(width: AppSpacing.sm),
            trend2 != null
                ? Expanded(
                    child: _buildSingleTrendCard(trend2),
                  )
                : Expanded(child: Container()), // Empty space if odd number
          ],
        ),
      );

      if (i + 2 < trendsData.length) {
        rows.add(SizedBox(height: AppSpacing.sm));
      }
    }

    return rows;
  }

  Widget _buildSingleTrendCard(Map<String, dynamic> trend) {
    return Container(
      margin: EdgeInsets.only(bottom: AppSpacing.xs),
      padding: EdgeInsets.all(AppSpacing.size10),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Color(0XFFB4B4B4)),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Trend icon based on value
          // Container(
          //   width: 40,
          //   height: 40,
          //   decoration: BoxDecoration(
          //     color: _getTrendColor(trend['value'] ?? '0%').withOpacity(0.1),
          //     shape: BoxShape.circle,
          //   ),
          //   child: Icon(
          //     _getTrendIcon(trend['value'] ?? '0%'),
          //     color: _getTrendColor(trend['value'] ?? '0%'),
          //     size: 20,
          //   ),
          // ),
          // SizedBox(width: AppSpacing.md),

          // Trend details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  trend['title'] ?? 'N/A',
                  style: FontManager.getCustomStyle(
                    fontSize: FontManager.s14,
                    fontWeight: FontManager.regular,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                ),
                SizedBox(height: AppSpacing.xs),
                Text(
                  trend['change'] ?? '',
                  style: FontManager.getCustomStyle(
                    fontSize: FontManager.s10,
                    fontWeight: FontManager.regular,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
          ),

          // Trend value
          Text(
            trend['value'] ?? 'N/A',
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s13,
              fontWeight: FontManager.bold,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to format date
  String _formatDate(String? isoDate) {
    if (isoDate == null) return 'DD/MM/YY';
    try {
      final date = DateTime.parse(isoDate);
      return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year.toString().substring(2)}';
    } catch (e) {
      return 'DD/MM/YY';
    }
  }

  // Date picker methods
  Future<void> _selectFromDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedFromDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (picked != null && picked != _selectedFromDate) {
      setState(() {
        _selectedFromDate = picked;
      });
      
      // Trigger date range change callback if both dates are selected
      if (_selectedToDate != null && widget.onDateRangeChanged != null) {
        widget.onDateRangeChanged!(picked, _selectedToDate!);
      }
    }
  }

  Future<void> _selectToDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedToDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (picked != null && picked != _selectedToDate) {
      setState(() {
        _selectedToDate = picked;
      });
      
      // Trigger date range change callback if both dates are selected
      if (_selectedFromDate != null && widget.onDateRangeChanged != null) {
        widget.onDateRangeChanged!(_selectedFromDate!, picked);
      }
    }
  }
}
