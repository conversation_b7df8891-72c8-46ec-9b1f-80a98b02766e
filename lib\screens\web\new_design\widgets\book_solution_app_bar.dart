import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import '../../../../providers/manual_creation_provider.dart';
import '../../../../providers/web_home_provider.dart';
import '../../../../providers/toggle_menu_provider.dart';
import '../../../../utils/responsive_font_sizes.dart';
import '../../../../utils/font_manager.dart';

class BookSolutionAppBar extends StatelessWidget
    implements PreferredSizeWidget {
  final String projectName;
  final bool showChatToggle;
  final bool isChatEnabled;
  final VoidCallback? onChatToggle;
  final bool showAIToggle;
  final bool isAIEnabled;
  final VoidCallback? onAIToggle;
  final VoidCallback? onAddModules;

  const BookSolutionAppBar({
    super.key,
    required this.projectName,
    this.showChatToggle = true,
    this.isChatEnabled = false,
    this.onChatToggle,
    this.showAIToggle = true,
    this.isAIEnabled = true,
    this.onAIToggle,
    this.onAddModules,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    // Calculate if we need compact mode based on available space
    // Rough calculation: Project name (~120px) + Chat/AI/Logout (~200px) + Navigation items (~600px) + padding
    final isCompact = screenWidth < 1366; // More aggressive threshold

    return Consumer2<ManualCreationProvider, WebHomeProvider>(
      builder: (context, manualCreationProvider, webHomeProvider, child) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Left section - Project Name only
              Row(
                children: [
                  Text(
                    projectName,
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleSmall(context),
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),

              // Center section with navigation tabs
              Expanded(
                child: isCompact
                    ? const SizedBox.shrink() // Empty in compact mode
                    : _buildFullNavigation(context, manualCreationProvider),
              ),

              // Right section - Chat with eye icon, AI/Manual toggle, and logout
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 3-dot menu in compact mode (positioned on the right)
                  if (isCompact) ...[
                    _buildExpandedMenu(manualCreationProvider),
                    const SizedBox(width: 10),
                  ],
                  // Chat with eye icon
                  if (showChatToggle) ...[
                    _buildChatButton(),
                    const SizedBox(width: 10),
                  ],

                  // AI/Manual Toggle
                  if (showAIToggle) ...[
                    _buildAIManualToggle(
                      context: context,
                      isAIEnabled: isAIEnabled,
                      onToggle: onAIToggle,
                    ),
                    const SizedBox(width: 10),
                  ],
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFullNavigation(
      BuildContext context, ManualCreationProvider manualCreationProvider) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildCountItem(
          context,
          'BRD',
          null,
          () {},
          isSelected: false,
        ),
        const SizedBox(width: 10),
        _buildCountItemWithIcon(
          context,
          '10 Agents',
          'assets/images/agent-icon.svg',
          _getAgentCount(manualCreationProvider),
          () {
            manualCreationProvider.handleAgentsTap();
          },
          isSelected: _isAgentsSelected(manualCreationProvider),
        ),
        const SizedBox(width: 10),
        _buildCountItemWithIcon(
          context,
          '102 Object',
          'assets/images/cube-box.svg',
          _getEntityCount(manualCreationProvider),
          () {
            manualCreationProvider.handleDataSetsTap();
          },
          isSelected: _isObjectSelected(manualCreationProvider),
        ),
        const SizedBox(width: 10),
        _buildCountItemWithIcon(
          context,
          'Workflows',
          'assets/images/square-box-uncheck.svg',
          _getWorkflowCount(manualCreationProvider),
          () {
            manualCreationProvider.handleWorkflowsTap();
          },
          isSelected: _isWorkflowsSelected(manualCreationProvider),
        ),
      ],
    );
  }

  Widget _buildChatButton() {
    return _ChatButtonWidget(
      isChatEnabled: isChatEnabled,
      onChatToggle: onChatToggle,
    );
  }

  Widget _buildExpandedMenu(ManualCreationProvider manualCreationProvider) {
    return ChangeNotifierProvider(
      create: (_) => ToggleMenuProvider(),
      child: Consumer<ToggleMenuProvider>(
        builder: (context, provider, child) {
          return MouseRegion(
            onEnter: (_) => provider.setHovered(true),
            onExit: (_) => provider.setHovered(false),
            child: InkWell(
              onTap: () {},
              hoverColor: Colors.transparent,
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
              splashFactory: NoSplash.splashFactory,
              child: Center(
                child: Container(
                  height: 28,
                  width: 28,
                  decoration: BoxDecoration(
                    color: provider.isMenuOpen
                        ? const Color(0xff0058FF)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(6.0),
                  ),
                  child: Center(
                    child: PopupMenuButton<String>(
                      icon: Icon(
                        Icons.more_vert,
                        size: 20,
                        color: provider.isMenuOpen
                            ? Colors.white
                            : provider.isHovered
                                ? const Color(0xff0058FF)
                                : Colors.grey.shade800,
                      ),
                      onSelected: (String value) {
                        switch (value) {
                          case 'brd':
                            // Handle BRD tap
                            break;
                          case 'agents':
                            manualCreationProvider.handleAgentsTap();
                            break;
                          case 'objects':
                            manualCreationProvider.handleDataSetsTap();
                            break;
                          case 'workflows':
                            manualCreationProvider.handleWorkflowsTap();
                            break;
                        }
                      },
                      onOpened: () {
                        provider.setMenuOpen(true);
                      },
                      onCanceled: () {
                        provider.setMenuOpen(false);
                      },
                      constraints: const BoxConstraints(
                        minWidth: 100,
                        maxWidth: 120,
                      ),
                      itemBuilder: (BuildContext context) =>
                          <PopupMenuEntry<String>>[
                        PopupMenuItem<String>(
                          value: 'brd',
                          height: 28,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.description_outlined,
                                size: 12,
                                color: Colors.grey.shade700,
                              ),
                              const SizedBox(width: 4),
                              Flexible(
                                child: Text(
                                  '1 BRD',
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    color: Colors.black,
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                        PopupMenuItem<String>(
                          value: 'agents',
                          height: 28,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SvgPicture.asset(
                                'assets/images/agent-icon.svg',
                                width: 12,
                                height: 12,
                                colorFilter: ColorFilter.mode(
                                  Colors.grey.shade700,
                                  BlendMode.srcIn,
                                ),
                              ),
                              const SizedBox(width: 4),
                              Flexible(
                                child: Text(
                                  '${_getAgentCount(manualCreationProvider) ?? 10} Agents',
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    color: Colors.black,
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                        PopupMenuItem<String>(
                          value: 'objects',
                          height: 28,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SvgPicture.asset(
                                'assets/images/cube-box.svg',
                                width: 12,
                                height: 12,
                                colorFilter: ColorFilter.mode(
                                  Colors.grey.shade700,
                                  BlendMode.srcIn,
                                ),
                              ),
                              const SizedBox(width: 4),
                              Flexible(
                                child: Text(
                                  '${_getEntityCount(manualCreationProvider) ?? 102} Object',
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    color: Colors.black,
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                        PopupMenuItem<String>(
                          value: 'workflows',
                          height: 28,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SvgPicture.asset(
                                'assets/images/square-box-uncheck.svg',
                                width: 12,
                                height: 12,
                                colorFilter: ColorFilter.mode(
                                  Colors.grey.shade700,
                                  BlendMode.srcIn,
                                ),
                              ),
                              const SizedBox(width: 4),
                              Flexible(
                                child: Text(
                                  '35 Solution',
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    color: Colors.black,
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                      offset: const Offset(0, 35),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                        side: const BorderSide(
                          color: Color(0xFF0058FF),
                          width: 0.5,
                        ),
                      ),
                      elevation: 8,
                      color: Colors.white,
                      splashRadius: 20,
                      padding: const EdgeInsets.symmetric(vertical: 4),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildCountItem(
      BuildContext context, String label, int? count, VoidCallback onTap,
      {bool isSelected = false}) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: onTap,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (count != null) ...[
              Text(
                count.toString(),
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.titleSmall(context),
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  color: isSelected ? const Color(0xFF0058FF) : Colors.black,
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
              const SizedBox(width: 4),
            ],
            Text(
              label,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.titleSmall(context),
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                color: isSelected ? const Color(0xFF0058FF) : Colors.black,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCountItemWithIcon(BuildContext context, String label,
      String iconPath, int? count, VoidCallback onTap,
      {bool isSelected = false}) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.all(4),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(
                iconPath,
                width: 12,
                height: 12,
                colorFilter: ColorFilter.mode(
                  isSelected ? const Color(0xFF0058FF) : Colors.black,
                  BlendMode.srcIn,
                ),
              ),
              const SizedBox(width: 6),
              if (count != null) ...[
                Text(
                  count.toString(),
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.titleSmall(context),
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                    color: isSelected ? const Color(0xFF0058FF) : Colors.black,
                    fontFamily: FontManager.fontFamilyTiemposText,
                  ),
                ),
                const SizedBox(width: 4),
              ],
              Text(
                label,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.titleSmall(context),
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  color: isSelected ? const Color(0xFF0058FF) : Colors.black,
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAIManualToggle({
    required BuildContext context,
    required bool isAIEnabled,
    required VoidCallback? onToggle,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          'AI',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.titleSmall(context),
            color: Colors.black,
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
          ),
        ),
        const SizedBox(width: 6),
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () {
              if (onToggle != null) {
                onToggle();
              }
            },
            child: Container(
              width: 34,
              height: 18,
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(
                  color: Colors.black,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(10),
              ),
              child: AnimatedAlign(
                duration: const Duration(milliseconds: 200),
                curve: Curves.easeInOut,
                alignment:
                    isAIEnabled ? Alignment.centerLeft : Alignment.centerRight,
                child: Container(
                  width: 16,
                  height: 16,
                  // margin: const EdgeInsets.all(2),
                  decoration: const BoxDecoration(
                    color: Color(0xFF0058FF),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ),
          ),
        ),
        const SizedBox(width: 6),
        Text(
          'Manual',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.titleSmall(context),
            color: Colors.black,
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
          ),
        ),
      ],
    );
  }

  // Helper methods to determine selected state
  bool _isAgentsSelected(ManualCreationProvider provider) {
    return provider.currentStep == WorkflowStep.agentCreation;
  }

  bool _isObjectSelected(ManualCreationProvider provider) {
    return provider.currentStep == WorkflowStep.entityCreation;
  }

  bool _isWorkflowsSelected(ManualCreationProvider provider) {
    return provider.currentStep == WorkflowStep.workflowCreation ||
        provider.currentStep == WorkflowStep.workflowLoCreation;
  }

  // Helper methods to get counts
  int? _getAgentCount(ManualCreationProvider provider) {
    if (provider.extractedAgentData?.agents != null) {
      return provider.extractedAgentData!.agents!.length;
    }
    return null;
  }

  int? _getEntityCount(ManualCreationProvider provider) {
    if (provider.extractedEntityData?.entityGroups != null) {
      return provider.extractedEntityData!.entityGroups!.length;
    }
    return null;
  }

  int? _getWorkflowCount(ManualCreationProvider provider) {
    if (provider.extractedWorkflowData != null) {
      return provider.extractedWorkflowData!.length;
    }
    return null;
  }

  @override
  Size get preferredSize => const Size.fromHeight(60);
}

class _ChatButtonWidget extends StatefulWidget {
  final bool isChatEnabled;
  final VoidCallback? onChatToggle;

  const _ChatButtonWidget({
    required this.isChatEnabled,
    required this.onChatToggle,
  });

  @override
  State<_ChatButtonWidget> createState() => _ChatButtonWidgetState();
}

class _ChatButtonWidgetState extends State<_ChatButtonWidget> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: GestureDetector(
        onTap: widget.onChatToggle,
        child: Container(
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            border: Border.all(
              color: isHovered ? const Color(0xFF0058FF) : Colors.transparent,
              width: 1,
            ),
            borderRadius: BorderRadius.circular(2),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                widget.isChatEnabled
                    ? Icons
                        .visibility_off_outlined // Eye with slash when chat is enabled
                    : Icons
                        .visibility_outlined, // Normal eye when chat is disabled
                color: Colors.black,
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                'Chat',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.titleSmall(context),
                  color: Colors.black,
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
