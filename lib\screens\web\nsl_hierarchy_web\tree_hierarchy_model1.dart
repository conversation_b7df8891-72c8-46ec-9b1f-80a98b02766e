import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/enhanced_go_lo_list_panel.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/nsl_heirarchy_model1.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/nsl_tree_side_details_new.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/metric_info_model.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/modern_side_panel1.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/unified_organizational_chart1.dart';
import 'package:nsl/services/nsl_hierarchy_api_service.dart';
import 'package:nsl/services/nsl_hierarchy_data_transformer.dart';

class GoLoPanel {
  final String id;
  final String metricType;

  GoLoPanel({
    required this.id,
    required this.metricType,
  });
}

class TreeHierarchyModel1 extends StatefulWidget {
  const TreeHierarchyModel1({super.key});

  @override
  State<TreeHierarchyModel1> createState() => _TreeHierarchyModelState();
}

class _TreeHierarchyModelState extends State<TreeHierarchyModel1> {
  List<NSLHierarchyData1> _nslNodes = [];
  NSLNode? _rootNode;
  NSLNode? _filteredRootNode;
  NSLHierarchyData1? _selectedNode;
  String? _selectedNodeId; // Track selected node ID for blue background
  bool _showSidePanel = false;
  int _clearSelectionTrigger = 0; // Trigger to clear selection in ModernSidePanel
  NslTreeSidePanel? _nodeDetails; // Store API response for node details
  bool _isLoadingNodeDetails = false; // Track loading state for node details
  MetricsInfo? _nodeTransactions; // Store API response for transactions data
  bool _isLoadingTransactions = false; // Track loading state for transactions
  final TextEditingController _searchController = TextEditingController();
  bool _isLoading = true;
  String _searchQuery = '';
  Map<String, dynamic>? _organizationalStructure;
  Map<String, dynamic>? _systemInfo;
   final ScrollController _horizontalScrollController = ScrollController();

  // New state for multiple GO/LO panels
  List<GoLoPanel> _activeGoLoPanels = [];


  
  // Side panel dimensions - calculated as 48% of screen width
  double _sidePanelWidth = 0.0; // Will be updated in build method
  double _minSidePanelWidth = 0.0; // Will be updated in build method
  double _maxSidePanelWidth = 0.0; // Will be updated in build method

  @override
  void initState() {
    super.initState();
    _loadNSLData();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text.trim();
      _buildFilteredTree();
    });
  }
  
  Future<void> _loadNSLData() async {
    try {
      // Fetch data from API
      final apiResponse = await NslHierarchyApiService.fetchModules();
      
      if (apiResponse == null) {
        print('Failed to fetch data from API');
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // Transform API data to hierarchy structure
      _rootNode = NslHierarchyDataTransformer.transformApiDataToHierarchy(apiResponse);
      
      if (_rootNode == null) {
        print('Failed to transform API data to hierarchy');
        setState(() {
          _isLoading = false;
        });
        return;
      }

      if (_rootNode != null) {
        _nslNodes = _rootNode!.originalData.getAllNodes(); // Flatten for search
      }

      // Create mock organizational structure for departments panel compatibility
      _organizationalStructure = {
        'M4': {
          'level_name': 'Executive Level',
          'node': {
            'id': _rootNode?.id ?? 'owner',
            'title': _rootNode?.title ?? 'CEO Operations',
            'type': 'MODULE',
          }
        },
        'M3': {'level_name': 'Department Level'},
        'M2': {'level_name': 'Team Level'},
        'M1': {'level_name': 'Individual Level'},
      };

      // Create mock system info
      _systemInfo = {
        'default_time_range': {
          'from': DateTime.now().subtract(Duration(days: 30)).toIso8601String(),
          'to': DateTime.now().toIso8601String(),
        },
        'last_updated': DateTime.now().toIso8601String(),
      };

      // Build filtered tree
      _buildFilteredTree();

      // Fetch root node details for the top bar
      if (_rootNode != null) {
        _fetchRootNodeDetails(_rootNode!.id);
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading NSL data from API: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _buildFilteredTree() {
    if (_searchQuery.isEmpty) {
      _filteredRootNode = _rootNode;
    } else {
      // Find node by title or ID in the flattened list
      final foundNodes = _nslNodes
          .where((node) =>
              node.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              node.id.toLowerCase().contains(_searchQuery.toLowerCase()))
          .toList();

      if (foundNodes.isNotEmpty) {
        // For nested structure, we need to find the node in the tree and show its subtree
        _filteredRootNode = _findNodeInTree(_rootNode, foundNodes.first.id);
      } else {
        _filteredRootNode = null;
      }
    }
  }

  // Helper method to find a node in the tree and return its subtree
  NSLNode? _findNodeInTree(NSLNode? node, String nodeId) {
    if (node == null) return null;
    
    if (node.id == nodeId) {
      return node;
    }
    
    for (var child in node.children) {
      final found = _findNodeInTree(child, nodeId);
      if (found != null) {
        return found;
      }
    }
    
    return null;
  }

  void _onNodeTitleTap(NSLNode node) async {
    // Title tap - opens side panel and sets selection
    setState(() {
      _selectedNode = node.originalData;
      _selectedNodeId = node.id; // Set selected node ID for blue background
      _showSidePanel = true;
      _activeGoLoPanels.clear();
      _clearSelectionTrigger++;
      _isLoadingNodeDetails = true; // Start loading node details
      _isLoadingTransactions = true; // Start loading transactions
      _nodeDetails = null; // Clear previous details
      _nodeTransactions = null; // Clear previous transactions
    });

    // Fetch node details and transactions from API
    try {
      // Fetch node details (GO/LO data)
      final nodeDetailsTask = NslHierarchyApiService.fetchNodeDetails(node.id);
      
      // Fetch transactions data (revenue, cost, margin, transactions)
      // Convert current date range to Unix seconds
      final now = DateTime.now();
      final fromDate = now.subtract(Duration(days: 30)); // Default 30 days ago
      final fromUnix = fromDate.millisecondsSinceEpoch ~/ 1000;
      final toUnix = now.millisecondsSinceEpoch ~/ 1000;
      
      final transactionsTask = NslHierarchyApiService.fetchNodeTransactions(node.id, fromUnix, toUnix);
      
      // Wait for both API calls to complete
      final results = await Future.wait([nodeDetailsTask, transactionsTask]);
      final nodeDetails = results[0] as NslTreeSidePanel?;
      final transactions = results[1] as MetricsInfo?;
      
      if (mounted) {
        setState(() {
          _nodeDetails = nodeDetails;
          _nodeTransactions = transactions;
          _isLoadingNodeDetails = false;
          _isLoadingTransactions = false;
        });
      }
    } catch (e) {
      print('Error fetching node data: $e');
      if (mounted) {
        setState(() {
          _isLoadingNodeDetails = false;
          _isLoadingTransactions = false;
        });
      }
    }
  }

  void _onNodeInfoTap(NSLNode node) {
    // Info tap - handled by the chart widget for expansion
    // This is called when info container is tapped but the expansion
    // logic is handled in the NSLHierarchyChart widget
    print('Info tapped for node: ${node.title}');
  }

  void _hideSidePanel() {
    setState(() {
      _showSidePanel = false;
      _selectedNode = null;
      _selectedNodeId = null; // Clear selection when side panel closes
      _activeGoLoPanels.clear(); // Close ALL GO/LO panels when main panel closes
      _clearSelectionTrigger++; // Increment trigger to clear selection in ModernSidePanel
    });
  }

  // Callback for arrow tap - opens new GO/LO panel
  void _onArrowTap(String metricType) {
    setState(() {
    // CLEAR all existing panels first
    _activeGoLoPanels.clear();
    
    // Normalize the metric type - remove "Total" prefix and convert to standard format
    String normalizedMetricType = metricType;
    if (metricType.toLowerCase().contains('total go')) {
      normalizedMetricType = 'GOs';
    } else if (metricType.toLowerCase().contains('total lo')) {
      normalizedMetricType = 'LOs';
    } else if (metricType.toLowerCase().contains('go')) {
      normalizedMetricType = 'GOs';
    } else if (metricType.toLowerCase().contains('lo')) {
      normalizedMetricType = 'LOs';
    }
    
    print('TreeHierarchy - Original metricType: $metricType, Normalized: $normalizedMetricType');
    
    // Then add the new panel
    _activeGoLoPanels.add(GoLoPanel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      metricType: normalizedMetricType,
    ));
  });
  }

  // Close specific GO/LO panel
  void _closeGoLoPanel(String panelId) {
    setState(() {
      _activeGoLoPanels.removeWhere((panel) => panel.id == panelId);
      _clearSelectionTrigger++; // Increment trigger to clear blue background in ModernSidePanel
    });
  }

  // Clear arrow selection when GO/LO panel is closed
  void _clearArrowSelection() {
    // This will be called when GO/LO panels are closed to clear the blue background
    // The ModernSidePanel will handle clearing its own selection state
  }

  // Fetch root node details for the top bar
  Future<void> _fetchRootNodeDetails(String nodeId) async {
    try {
      final nodeDetails = await NslHierarchyApiService.fetchNodeDetails(nodeId);
      if (mounted) {
        setState(() {
          _nodeDetails = nodeDetails;
        });
      }
    } catch (e) {
      print('Error fetching root node details: $e');
    }
  }

  // Handle date range change - refetch transactions data with new dates
  void _onDateRangeChanged(DateTime fromDate, DateTime toDate) async {
    if (_selectedNode == null) return;

    print('Date range changed: ${fromDate.toIso8601String()} to ${toDate.toIso8601String()}');
    
    setState(() {
      _isLoadingTransactions = true;
      _nodeTransactions = null; // Clear current data
    });

    try {
      // Convert dates to Unix timestamps
      final fromUnix = fromDate.millisecondsSinceEpoch ~/ 1000;
      final toUnix = toDate.millisecondsSinceEpoch ~/ 1000;
      
      // Fetch new transactions data with selected date range
      final transactions = await NslHierarchyApiService.fetchNodeTransactions(
        _selectedNode!.id, 
        fromUnix, 
        toUnix
      );
      
      if (mounted) {
        setState(() {
          _nodeTransactions = transactions;
          _isLoadingTransactions = false;
        });
      }
    } catch (e) {
      print('Error fetching transactions for date range: $e');
      if (mounted) {
        setState(() {
          _isLoadingTransactions = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    // Calculate side panel dimensions as 48% of screen width
    final screenWidth = MediaQuery.of(context).size.width;
    final desiredSidePanelWidth = screenWidth * 0.48;
    
    // Update side panel dimensions
    _sidePanelWidth = desiredSidePanelWidth;
    _minSidePanelWidth = desiredSidePanelWidth * 0.8; // 80% of desired as minimum
    _maxSidePanelWidth = desiredSidePanelWidth * 1.2; // 120% of desired as maximum
  final totalGoLoPanelsWidth = screenWidth * 0.16 * _activeGoLoPanels.length;
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          // Main content row
          Row(
            children: [
              // Unified organizational chart (combines left panel + hierarchy chart)
              Expanded(
                flex: _showSidePanel ? 3 : 1,
                child: _filteredRootNode != null
                    ? UnifiedOrganizationalChart1(
                        organizationalStructure: _organizationalStructure,
                        rootNode: _filteredRootNode!,
                        selectedNodeId: _selectedNodeId, // Pass selected node ID
                        isSidePanelOpen: _showSidePanel, // Pass side panel state
                        sidePanelWidth: _sidePanelWidth, // Pass side panel width
                        rootNodeDetails: _nodeDetails, // Pass API node details for root
                        onNodeTitleTap: _onNodeTitleTap,
                        onNodeInfoTap: _onNodeInfoTap,
                      )
                    : _buildEmptyState(),
              ),
              // Side panel for NSL node details
              if (_showSidePanel && _selectedNode != null)
                SizedBox(
                   width: _sidePanelWidth, 
                  child: ModernSidePanel1(
                    nodeData: _selectedNode,
                    nodeDetails: _nodeDetails, // Pass API node details
                    isLoadingNodeDetails: _isLoadingNodeDetails, // Pass loading state
                    nodeTransactions: _nodeTransactions, // Pass transactions data
                    isLoadingTransactions: _isLoadingTransactions, // Pass loading state for transactions
                    dateRange: _systemInfo?['default_time_range'],
                    onClose: _hideSidePanel,
                    onArrowTap: _onArrowTap, // Pass arrow tap callback
                    onGoLoPanelClosed: _clearArrowSelection, // Pass callback to clear selection
                    clearSelectionTrigger: _clearSelectionTrigger, // Pass trigger to clear selection
                    onDateRangeChanged: _onDateRangeChanged, // Pass date change callback
                  ),
                ),
              
              // Multiple GO/LO Panels (20% each for enhanced panel)
              ..._activeGoLoPanels.map((panel) => 
                Container(
                  width: screenWidth * 0.16,
                  child: EnhancedGoLoListPanel(
                    panelId: panel.id,
                    metricType: panel.metricType,
                    onClose: () => _closeGoLoPanel(panel.id),
                  ),
                ),
              ).toList(),
            ],
          ),
          
          // Positioned close button - floats over content
          // if (_showSidePanel && _selectedNode != null)
          //   Positioned(
          //     top: 20 ,
          //      right: _sidePanelWidth + totalGoLoPanelsWidth + 8,  // Position it just outside the side panel
          //     child: Container(
              
          //       padding:EdgeInsets.symmetric(horizontal: 5,vertical:1),
          //       decoration: BoxDecoration(
          //           color: Colors.white,
          //              border: Border.all(color: Color(0xFFB4B4B4)),
          //               borderRadius: BorderRadius.circular(4),
          //             ),
          //       child: InkWell(
          //        // borderRadius: BorderRadius.circular(8),
          //         onTap: _hideSidePanel,
          //         child: Container(
          //          padding:EdgeInsets.symmetric(horizontal: 10,vertical:5),
          //           child: Text('Close',
          //           style: TextStyle(
          //           fontSize: 10,
          //       color: Colors.black,
          //       fontFamily: 'TiemposText',)),
          //         ),
          //       ),
          //     ),
          //   ),
       
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: Row(
        children: [
          // Title
          Text(
            'NSL Hierarchy',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              fontFamily: 'TiemposText',
              color: Colors.black,
            ),
          ),

          const Spacer(),

          // Search field
          Container(
            width: 300,
            height: 40,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search by title or ID...',
                hintStyle: TextStyle(
                  color: Colors.grey.shade500,
                  fontSize: 14,
                  fontFamily: 'TiemposText',
                ),
                prefixIcon: Icon(
                  Icons.search,
                  color: Colors.grey.shade500,
                  size: 20,
                ),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: Icon(
                          Icons.clear,
                          color: Colors.grey.shade500,
                          size: 20,
                        ),
                        onPressed: () {
                          _searchController.clear();
                        },
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'TiemposText',
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isEmpty
                ? 'No NSL hierarchy data available'
                : 'No nodes found for "$_searchQuery"',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
              fontFamily: 'TiemposText',
            ),
          ),
          if (_searchQuery.isNotEmpty) ...[
            const SizedBox(height: 8),
            TextButton(
              onPressed: () {
                _searchController.clear();
              },
              child: Text(
                'Clear search',
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xff0058FF),
                  fontFamily: 'TiemposText',
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
