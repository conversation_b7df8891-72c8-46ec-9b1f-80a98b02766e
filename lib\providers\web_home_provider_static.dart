import 'package:flutter/material.dart';
import 'package:nsl/models/entities_data.dart';
import 'package:nsl/models/multimedia/file_upload_ocr_response.dart';
import 'package:nsl/models/role_info.dart';
import 'package:nsl/models/solution/solution_session_model.dart';
import 'package:nsl/models/solution/solution_status_model.dart';
import 'package:nsl/models/mode.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../providers/base_provider.dart';
import '../services/auth_service.dart';
import '../services/chat_api_service.dart';
import '../utils/logger.dart';
import '../models/message_response.dart';
import '../utils/screen_constants.dart';
import '../models/chat_message.dart';
import 'dart:math' as math;

class WebHomeProviderStatic extends BaseProvider {
  // Chat API service
  final ChatApiService _chatApiService = ChatApiService();

  // Auth service for getting user ID
  final AuthService _authService = AuthService();

  // Chat history
  List<Map<String, dynamic>> _chatHistory = [];

  // Chat messages
  final List<ChatMessage> _messages = [];

  // Store conversation ID
  String? _conversationId;

  // Flag to track if this is the first message in a session
  bool _isFirstMessage = true;

  // Store the conversation session ID for the new API
  String? _currentSessionId;
  String? _nodeId;

  // Current screen name for navigation (using string instead of index for reliability)
  String _currentScreenIndex = ScreenConstants.home;

  String _solutionWidgetsSelectedFrom = "";

  // Chat history expansion state
  bool _isChatHistoryExpanded = false;

  // Store the last user message for API calls (including OCR text)
  String _lastUserMessageForApi = '';

  // Default heights for chat field
  // final double _homeScreenChatHeight = 130.0;
  // final double _otherScreenChatHeight = 60.0;

  // Custom heights that can be set
  // double? _customHomeScreenChatHeight;
  // double? _customOtherScreenChatHeight;

  bool _isLoading = false;
  bool _showSidePanel = false;
  bool _hasTextInChatField = false;

  bool _showStatus = false;
  bool _showStatusArtifacts = false;

  // Mobile chat expansion state
  bool _isMobileChatExpanded = false;

  // OCR panel state
  bool _showOcrPanel = false;
  String _ocrText = '';
  String _ocrFileName = '';
  bool _showGlobalLibrary = false;

  // Quick message selection
  String? _selectedQuickMessage = "Solution";
  // = "NSL";

  // Available modes from API
  List<Mode> _availableModes = [];
  bool _isLoadingModes = false;
  String? _selectedModeId;

  // Mode session management
  String? _modeSessionId;
  bool _isFirstModeMessage = true;

  // Solution dropdown selection
  String? _selectedSolutionOption;

  // NSL Thinking expansion state - maps message index to expansion state
  final Map<int, bool> _nslThinkingExpanded = {};

  // Entity selection state
  Entity? _selectedEntity;
  int _selectedSectionIndex = 0;

  // Role selection state
  RoleInfo? _selectedRole;

  // Workflow selection state
  Map<String, dynamic>? _selectedWorkflow;

  // Circuit board tab selection
  String _selectedCircuitTab = 'Synthetic';

  // Track visibility of BookSolutionAppBar
  bool _showBookSolutionAppBar = false;

  // Track visibility of SolutionDetailsPanel
  bool _showSolutionDetailsPanel = false;

  bool _isProjectCreated = false;

  // Getter for conversation ID
  String? get conversationId => _conversationId;

  // Getter for first message flag
  bool get isFirstMessage => _isFirstMessage;

  // Getter for current session ID
  String? get currentSessionId => _currentSessionId;
  String? get nodeId => _nodeId;

  // Getter for current screen name
  String get currentScreenIndex => _currentScreenIndex;
  String get solutionWidgetsSelectedFrom => _solutionWidgetsSelectedFrom;

  // Getter for chat history expanded state
  bool get isChatHistoryExpanded => _isChatHistoryExpanded;
  bool get showGlobalLibrary => _showGlobalLibrary;

  // Chat UI state getters and setters
  List<ChatMessage> get messages => _messages;

  bool get isProjectCreated => _isProjectCreated;

  set isProjectCreated(value) {
    _isProjectCreated = value;
    notifyListeners();
  }

  @override
  bool get isLoading => _isLoading;

  bool get showSidePanel => _showSidePanel;
  bool get hasTextInChatField => _hasTextInChatField;

  bool get showStatus => _showStatus;
  bool get showStatusArtifacts => _showStatusArtifacts;

  // Mobile chat expansion state getter and setter
  bool get isMobileChatExpanded => _isMobileChatExpanded;

  // OCR panel state getters and setters
  bool get showOcrPanel => _showOcrPanel;
  String get ocrText => _ocrText;
  String get ocrFileName => _ocrFileName;

  // Quick message selection getter and setter
  String? get selectedQuickMessage => _selectedQuickMessage;

  // Available modes getters
  List<Mode> get availableModes => _availableModes;
  bool get isLoadingModes => _isLoadingModes;
  String? get selectedModeId => _selectedModeId;

  // Solution dropdown selection getter and setter
  String? get selectedSolutionOption => _selectedSolutionOption;

  // NSL Thinking expansion state getter
  Map<int, bool> get nslThinkingExpanded => _nslThinkingExpanded;

  // Entity selection state getters and setters
  Entity? get selectedEntity => _selectedEntity;
  int get selectedSectionIndex => _selectedSectionIndex;

  // Role selection state getter and setter
  RoleInfo? get selectedRole => _selectedRole;

  // Workflow selection state getter and setter
  Map<String, dynamic>? get selectedWorkflow => _selectedWorkflow;

  // Circuit board tab selection getter and setter
  String get selectedCircuitTab => _selectedCircuitTab;

  // BookSolutionAppBar visibility getter and setter
  bool get showBookSolutionAppBar => _showBookSolutionAppBar;
  set showBookSolutionAppBar(bool show) {
    _showBookSolutionAppBar = show;
    notifyListeners();
  }

  // SolutionDetailsPanel visibility getter and setter
  bool get showSolutionDetailsPanel => _showSolutionDetailsPanel;
  set showSolutionDetailsPanel(bool show) {
    _showSolutionDetailsPanel = show;
    notifyListeners();
  }

  TextEditingController chatController = TextEditingController();

  String selectedSolutionName = "";

  SolutionSessionModel? solutionSessionModel;
  SolutionSessionModel? selectedSolutionSessionModel;

  SolutionStatusModel? solutionStatusModel;
  String? lastSolutionMessage;
  Map<String, dynamic>? lastSolutionResponse;

  // Last user message for API calls getter and setter
  String get lastUserMessageForApi => _lastUserMessageForApi;
  set lastUserMessageForApi(String message) {
    _lastUserMessageForApi = message;
    Logger.info('Set lastUserMessageForApi: $message');
  }

  // Add a message to the messages list
  void addMessage(ChatMessage message) {
    _messages.add(message);
    notifyListeners();
  }

  // Replace a message at a specific index
  void replaceMessageAt(int index, ChatMessage message) {
    if (index >= 0 && index < _messages.length) {
      _messages[index] = message;
      notifyListeners();
    }
  }

  // Clear all messages
  void clearMessages() {
    _messages.clear();
    notifyListeners();
  }

  // Set loading state
  set isLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Set side panel visibility
  set showSidePanel(bool show) {
    _showSidePanel = show;
    notifyListeners();
  }

  // Set text in chat field state
  set hasTextInChatField(bool hasText) {
    _hasTextInChatField = hasText;
    notifyListeners();
  }

  // Set mobile chat expansion state
  set isMobileChatExpanded(bool expanded) {
    _isMobileChatExpanded = expanded;
    notifyListeners();
  }

  // Toggle mobile chat expansion state
  void toggleMobileChatExpansion() {
    _isMobileChatExpanded = !_isMobileChatExpanded;
    notifyListeners();
  }

  // Set OCR panel visibility
  set showOcrPanel(bool show) {
    _showOcrPanel = show;
    notifyListeners();
  }

  // Clear selected quick message
  void clearQuickMessage() {
    _selectedQuickMessage = '';
    notifyListeners();
  }

  // Set OCR text
  set ocrText(String text) {
    _ocrText = text;
    notifyListeners();
  }

  // Set OCR file name
  set ocrFileName(String name) {
    _ocrFileName = name;
    notifyListeners();
  }

  // Set selected quick message
  set selectedQuickMessage(String? message) {
    _selectedQuickMessage = message;
    notifyListeners();
  }

  // Set selected solution option
  set selectedSolutionOption(String? option) {
    _selectedSolutionOption = option;
    notifyListeners();
  }

  // Set selected mode ID
  set selectedModeId(String? modeId) {
    _selectedModeId = modeId;
    notifyListeners();
  }

  // Set NSL thinking expanded state for a specific message
  void setNslThinkingExpanded(int index, bool expanded) {
    _nslThinkingExpanded[index] = expanded;
    notifyListeners();
  }

  // Clear all NSL thinking expanded states
  void clearNslThinkingExpanded() {
    _nslThinkingExpanded.clear();
    notifyListeners();
  }

  // Set selected entity
  set selectedEntity(Entity? entity) {
    _selectedEntity = entity;
    notifyListeners();
  }

  // Set selected section index
  set selectedSectionIndex(int index) {
    _selectedSectionIndex = index;
    notifyListeners();
  }

  // Set selected role
  set selectedRole(RoleInfo? role) {
    _selectedRole = role;
    notifyListeners();
  }

  // Set selected workflow
  set selectedWorkflow(Map<String, dynamic>? workflow) {
    _selectedWorkflow = workflow;
    notifyListeners();
  }

  // Set selected circuit board tab
  set selectedCircuitTab(String tab) {
    _selectedCircuitTab = tab;
    notifyListeners();
  }

  // Setter for current session ID
  set currentSessionId(String? sessionId) {
    _currentSessionId = sessionId;
    notifyListeners();
  }

  // Setter for current screen name
  set currentScreenIndex(String screenName) {
    // Only update if the value is different and not empty
    if (_currentScreenIndex != screenName && screenName.isNotEmpty) {
      _currentScreenIndex = screenName;
      Logger.info('Set WebHomeProvider currentScreenIndex to $screenName');
      // Save the updated screen name to SharedPreferences
      _saveCurrentScreenIndex();
      notifyListeners();
    } else if (screenName.isEmpty) {
      // If value is empty, log it but don't update or notify
      Logger.info(
          'Attempted to set currentScreenIndex to empty string - ignoring');
    }
  }

  set solutionWidgetsSelectedFrom(String screenName) {
    // Only update if the value is different and not empty
    if (_solutionWidgetsSelectedFrom != screenName) {
      _solutionWidgetsSelectedFrom = screenName;
      Logger.info('Set WebHomeProvider currentScreenIndex to $screenName');
      // Save the updated screen name to SharedPreferences
      _saveSolutionWidgetsIndex();
      notifyListeners();
    } else if (screenName.isEmpty) {
      // If value is empty, log it but don't update or notify
      Logger.info(
          'Attempted to set currentScreenIndex to empty string - ignoring');
    }
  }

  set showGlobalLibrary(value) {
    _showGlobalLibrary = value;
    notifyListeners();
  }

  // Save current screen name to SharedPreferences
  Future<void> _saveCurrentScreenIndex() async {
    try {
      // Only save if the current screen index is not empty
      if (_currentScreenIndex.isNotEmpty) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('current_screen_index', _currentScreenIndex);
        Logger.info('Saved current screen index: $_currentScreenIndex');
      } else {
        Logger.info('Not saving empty current screen index');
      }
    } catch (e) {
      Logger.error('Error saving current screen index: $e');
    }
  }

  Future<void> _saveSolutionWidgetsIndex() async {
    try {
      // Only save if the current screen index is not empty
      if (_solutionWidgetsSelectedFrom.isNotEmpty) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(
            'solution_widgets_index', _solutionWidgetsSelectedFrom);
        Logger.info(
            'Saved solution screen index: $_solutionWidgetsSelectedFrom');
      } else {
        Logger.info('Not saving empty current screen index');
      }
    } catch (e) {
      Logger.error('Error saving current screen index: $e');
    }
  }

  // Setter for chat history expanded state
  set isChatHistoryExpanded(bool expanded) {
    _isChatHistoryExpanded = expanded;
    notifyListeners();
    // Save the updated chat history expanded state to SharedPreferences
    _saveChatHistoryExpandedState();
  }

  // Save chat history expanded state to SharedPreferences
  Future<void> _saveChatHistoryExpandedState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('is_chat_history_expanded', _isChatHistoryExpanded);
      Logger.info('Saved chat history expanded state: $_isChatHistoryExpanded');
    } catch (e) {
      Logger.error('Error saving chat history expanded state: $e');
    }
  }

  WebHomeProvider() {
    _loadConversationState();
    // // Force set to webMyLibrary to ensure first item is active
    // _currentScreenIndex = ScreenConstants.webMyLibrary;
    // _saveCurrentScreenIndex();
  }

  // Method to set screen index for manual creation static screen
  void setManualCreationStaticScreen() {
    _currentScreenIndex = ScreenConstants.manualCreationStaticScreen;
    _saveCurrentScreenIndex();
    notifyListeners();
  }

  // Load conversation state from SharedPreferences
  Future<void> _loadConversationState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _conversationId = prefs.getString('general_conversation_id');
      _isFirstMessage = prefs.getBool('is_first_message') ?? true;
      _currentSessionId = prefs.getString('current_session_id');

      // Handle migration from int to string for screen index
      if (prefs.containsKey('current_screen_index')) {
        if (prefs.getInt('current_screen_index') != null) {
          // Convert old int value to string name
          final int oldIndex = prefs.getInt('current_screen_index')!;
          switch (oldIndex) {
            case 0:
              _currentScreenIndex = ScreenConstants.home;
              break;
            case 1:
              _currentScreenIndex = ScreenConstants.create;
              break;
            case 6:
              _currentScreenIndex = ScreenConstants.nslJava;
              break;
            default:
              _currentScreenIndex = ScreenConstants.home;
          }
          // Save the new string value
          await prefs.setString('current_screen_index', _currentScreenIndex);
          // Remove the old int value
          await prefs.remove('current_screen_index_int');
        } else {
          // Get the string value
          _currentScreenIndex =
              prefs.getString('current_screen_index') ?? ScreenConstants.home;
        }
      } else {
        _currentScreenIndex = ScreenConstants.home;
      }

      _isChatHistoryExpanded =
          prefs.getBool('is_chat_history_expanded') ?? false;

      Logger.info(
          'Loaded conversation state: ID=$_conversationId, isFirstMessage=$_isFirstMessage, sessionId=$_currentSessionId, screenName=$_currentScreenIndex, isChatHistoryExpanded=$_isChatHistoryExpanded');
    } catch (e) {
      Logger.error('Error loading conversation state: $e');
      _isFirstMessage = true;
      _conversationId = null;
      _currentSessionId = null;
      _currentScreenIndex = ScreenConstants.home;
      _isChatHistoryExpanded = false;
    }
  }

  // Save conversation state to SharedPreferences
  Future<void> _saveConversationState() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      if (_conversationId != null) {
        await prefs.setString('general_conversation_id', _conversationId!);
      }

      if (_currentSessionId != null) {
        await prefs.setString('current_session_id', _currentSessionId!);
      }

      if (_nodeId != null) {
        await prefs.setString('current_node_id', _nodeId!);
      }

      await prefs.setBool('is_first_message', _isFirstMessage);
      await prefs.setString('current_screen_index', _currentScreenIndex);
      await prefs.setBool('is_chat_history_expanded', _isChatHistoryExpanded);

      Logger.info(
          'Saved conversation state: ID=$_conversationId, isFirstMessage=$_isFirstMessage, sessionId=$_currentSessionId, screenName=$_currentScreenIndex, isChatHistoryExpanded=$_isChatHistoryExpanded');
    } catch (e) {
      Logger.error('Error saving conversation state: $e');
    }
  }

  // Reset conversation state
  Future<void> resetConversation({bool preserveScreenIndex = true}) async {
    try {
      // Clear the chat history
      _chatHistory.clear(); // Clear the chat input field
      chatController.clear();
      _conversationId = null;
      _isFirstMessage = true;
      _currentSessionId = null;
      _nodeId = null;
      _showStatus = false;
      _showStatusArtifacts = false;
      _isLoading = false;
      _showBookSolutionAppBar = false; // Reset BookSolutionAppBar flag
      _showSolutionDetailsPanel = false;
      _showGlobalLibrary = false; // Reset GlobalLibrary flag
      _isProjectCreated = false; // Reset SolutionDetailsPanel flag
      _lastUserMessageForApi = ''; // Reset the last user message
      solutionSessionModel = null; // Reset solution session model
      selectedSolutionSessionModel =
          null; // Reset selected solution session model
      solutionStatusModel = null; // Reset solution status model
      messages.clear();
      resetUIState(preserveScreenIndex: preserveScreenIndex);
      // We don't reset currentScreenIndex here as it's a navigation state
      resetModeSession();
      _selectedQuickMessage =
          _availableModes.isNotEmpty ? _availableModes[0].name : "Solution";
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('general_conversation_id');
      await prefs.remove('current_session_id');
      await prefs.setBool('is_first_message', true);
      await prefs.remove('current_session_id');
      await prefs.remove('current_node_id');
      // Keep the current screen name in SharedPreferences      Logger.info('Reset conversation state - all UI and state variables have been reset');
      Logger.info('showBookSolutionAppBar: $_showBookSolutionAppBar');
      Logger.info('showSolutionDetailsPanel: $_showSolutionDetailsPanel');
      Logger.info('showGlobalLibrary: $_showGlobalLibrary');
      Logger.info('isProjectCreated: $_isProjectCreated');
      Logger.info('currentSessionId: $_currentSessionId');
      Logger.info('nodeId: $_nodeId');

      notifyListeners();
    } catch (e) {
      Logger.error('Error resetting conversation state: $e');
    }
  }

  // Reset UI state
  void resetUIState({bool preserveScreenIndex = true}) {
    // Clear messages
    _messages.clear();

    // Reset chat UI state
    _isLoading = false;
    _showSidePanel = false;
    _hasTextInChatField = false;

    // Reset mobile chat expansion state
    _isMobileChatExpanded = false; // Reset OCR panel state
    _showOcrPanel = false;
    _ocrText = '';
    _ocrFileName = '';

    // Reset BookSolutionAppBar flag
    _showBookSolutionAppBar = false;

    // Reset SolutionDetailsPanel flag
    _showSolutionDetailsPanel = false;

    // Reset GlobalLibrary flag
    _showGlobalLibrary = false;

    // Reset solution models
    solutionSessionModel = null;
    selectedSolutionSessionModel = null;
    solutionStatusModel = null;

    // Reset API message
    _lastUserMessageForApi = '';

    // Reset session IDs that might have been missed
    _currentSessionId = null;
    _nodeId = null;

    // Reset quick message selection
    _selectedQuickMessage =
        // "NSL";
        "Solution";
    // Reset solution dropdown selection
    _selectedSolutionOption = null;

    // Clear NSL thinking expanded states
    _nslThinkingExpanded.clear();

    // Reset entity and role selection
    _selectedEntity = null;
    _selectedRole = null;
    _selectedWorkflow = null;
    _selectedSectionIndex = 0;

    // Reset circuit board tab
    _selectedCircuitTab =
        'Synthetic'; // Only set the current screen index to home if not preserving it
    if (!preserveScreenIndex) {
      _currentScreenIndex = ScreenConstants.home;
      // Save the updated screen name to SharedPreferences
      _saveCurrentScreenIndex();
    }

    // Log the reset state
    Logger.info('UI state reset completed');
    Logger.info('showGlobalLibrary: $_showGlobalLibrary');
    Logger.info('showSolutionDetailsPanel: $_showSolutionDetailsPanel');

    // Ensure we notify listeners of the changes
    notifyListeners();
  }

  // Helper method to create a title from the first message
  String _createTitleFromMessage(String message) {
    String title = message.trim();
    if (title.length > 50) {
      title = '${title.substring(0, 47)}...';
    }
    return title;
  }

  // Process the response and extract reasoning data if available
  Map<String, dynamic> _processApiResponse(Map<String, dynamic> response) {
    if (!response['success']) {
      return response;
    }

    try {
      // Try to parse the response data as a MessageResponse
      if (response['data'] != null) {
        final data = response['data'];

        // Create a MessageResponse object from the data
        final messageResponse = MessageResponse.fromJson(data);

        // Extract the reasoning data from the MessageResponse
        if (messageResponse.reasoning.isNotEmpty) {
          // Extract just the content from each Reasoning object
          List<String> reasoningContents = messageResponse.reasoning
              .map((reasoning) => reasoning.content)
              .toList();

          // Add the reasoning contents to the response
          response['reasoning'] = reasoningContents;
        }
      }
    } catch (e) {
      // If parsing as MessageResponse fails, fall back to the old method
      Logger.error('Error parsing MessageResponse: $e');

      // Extract reasoning data if available using the old method
      List<String>? reasoningList;
      if (response['data'] != null) {
        final data = response['data'];

        // Check for reasoning in different possible formats
        if (data['reasoning'] != null) {
          var reasoning = data['reasoning'];

          // Convert reasoning to List<String> regardless of its original format
          if (reasoning is List) {
            reasoningList = reasoning.map((item) {
              if (item is Map && item.containsKey('content')) {
                return item['content'].toString();
              }
              return item.toString();
            }).toList();
          } else if (reasoning is String) {
            reasoningList = [reasoning];
          }
        }
      }

      // Add reasoning to the response
      if (reasoningList != null) {
        response['reasoning'] = reasoningList;
      }
    }

    return response;
  }

  // Create a new conversation
  Future<Map<String, dynamic>> createConversation(
      [String? userId, String? title]) async {
    // Get the user ID from AuthService if not provided
    String? userIdStr = userId?.toString() ?? await _authService.getUserId();
    String actualUserId = userIdStr ?? "1";

    // Use a default title if not provided
    String actualTitle = title ?? 'New Conversation';

    Logger.info(
        'Creating conversation for user ID: $actualUserId with title: $actualTitle');

    return await runWithLoadingAndErrorHandling<Map<String, dynamic>>(
          () async {
            // Call the service to create a conversation
            final response = await _chatApiService.createConversation(
                actualUserId, actualTitle);

            if (response['success']) {
              // Extract conversation ID from response
              final conversationId = response['conversation_id'];

              if (conversationId != null) {
                // Update state
                _conversationId = conversationId;
                _isFirstMessage = false;
                await _saveConversationState();

                Logger.info(
                    'Created new conversation with ID: $_conversationId');
              }
            }

            return response;
          },
          context: 'WebHomeProvider.createConversation',
        ) ??
        {'success': false, 'message': 'Failed to create conversation'};
  }

  // Send a general question to the API
  Future<Map<String, dynamic>> sendGeneralQuestion(String question,
      [int? userId]) async {
    // Get the user ID from AuthService if not provided
    String? userIdStr = userId?.toString() ?? await _authService.getUserId();
    String? actualUserId = userIdStr ?? "1";

    // Use lastUserMessageForApi if available (for OCR text), otherwise use the original question
    String messageToSend =
        _lastUserMessageForApi.isNotEmpty ? _lastUserMessageForApi : question;

    // Log the message being sent
    if (_lastUserMessageForApi.isNotEmpty) {
      Logger.info('Using enhanced message with OCR text for API call');
      Logger.info('Original question: $question');
      Logger.info('Enhanced message: $messageToSend');
    }

    Logger.info('Sending general question for user ID: $actualUserId');

    // If this is the first message, create a conversation first
    if (_isFirstMessage) {
      // Use the first message as the title (always use original question for title)
      String title = _createTitleFromMessage(question);

      final createResult = await createConversation(actualUserId, title);

      if (!createResult['success']) {
        return createResult; // Return the error from create conversation
      }

      // Now we have a conversation ID, proceed with sending the question
    }

    // Ensure we have a conversation ID
    if (_conversationId == null) {
      Logger.error('No conversation ID available for sending question');
      return {
        'success': false,
        'message': 'No conversation ID available',
      };
    }

    final result = await runWithLoadingAndErrorHandling<Map<String, dynamic>>(
          () async {
            // Call the service to send the question with the enhanced message
            final response = await _chatApiService.sendGeneralQuestion(
                messageToSend, _conversationId!, actualUserId);

            // Reset lastUserMessageForApi after sending
            _lastUserMessageForApi = '';

            return _processApiResponse(response);
          },
          context: 'WebHomeProvider.sendGeneralQuestion',
        ) ??
        {'success': false, 'message': 'Failed to send question'};

    return result;
  }

  // Send an internet question to the API
  Future<Map<String, dynamic>> sendInternetQuestion(String question,
      [int? userId]) async {
    // Get the user ID from AuthService if not provided
    String? userIdStr = userId?.toString() ?? await _authService.getUserId();
    String actualUserId = userIdStr ?? "1";

    // Use lastUserMessageForApi if available (for OCR text), otherwise use the original question
    String messageToSend =
        _lastUserMessageForApi.isNotEmpty ? _lastUserMessageForApi : question;

    // Log the message being sent
    if (_lastUserMessageForApi.isNotEmpty) {
      Logger.info('Using enhanced message with OCR text for API call');
      Logger.info('Original question: $question');
      Logger.info('Enhanced message: $messageToSend');
    }

    Logger.info('Sending internet question for user ID: $actualUserId');

    // If this is the first message, create a conversation first
    if (_isFirstMessage) {
      // Use the first message as the title (always use original question for title)
      String title = _createTitleFromMessage(question);

      final createResult = await createConversation(actualUserId, title);

      if (!createResult['success']) {
        return createResult; // Return the error from create conversation
      }

      // Now we have a conversation ID, proceed with sending the question
    }

    // Ensure we have a conversation ID
    if (_conversationId == null) {
      Logger.error(
          'No conversation ID available for sending internet question');
      return {
        'success': false,
        'message': 'No conversation ID available',
      };
    }

    final result = await runWithLoadingAndErrorHandling<Map<String, dynamic>>(
          () async {
            // Call the service to send the question with the enhanced message
            final response = await _chatApiService.sendInternetQuestion(
                messageToSend, _conversationId!, actualUserId);

            // Reset lastUserMessageForApi after sending
            _lastUserMessageForApi = '';

            return _processApiResponse(response);
          },
          context: 'WebHomeProvider.sendInternetQuestion',
        ) ??
        {'success': false, 'message': 'Failed to send internet question'};

    return result;
  }

  // Send an NSL question to the API
  Future<Map<String, dynamic>> sendNslQuestion(String question,
      [int? userId]) async {
    // Get the user ID from AuthService if not provided
    String? userIdStr = userId?.toString() ?? await _authService.getUserId();
    String actualUserId = userIdStr ?? "1";

    // Use lastUserMessageForApi if available (for OCR text), otherwise use the original question
    String messageToSend =
        _lastUserMessageForApi.isNotEmpty ? _lastUserMessageForApi : question;

    // Log the message being sent
    if (_lastUserMessageForApi.isNotEmpty) {
      Logger.info('Using enhanced message with OCR text for API call');
      Logger.info('Original question: $question');
      Logger.info('Enhanced message: $messageToSend');
    }

    Logger.info('Sending NSL question for user ID: $actualUserId');

    // If this is the first message, create a conversation first
    if (_isFirstMessage) {
      // Use the first message as the title (always use original question for title)
      String title = _createTitleFromMessage(question);

      final createResult = await createConversation(actualUserId, title);

      if (!createResult['success']) {
        return createResult; // Return the error from create conversation
      }

      // Now we have a conversation ID, proceed with sending the question
    }

    // Ensure we have a conversation ID
    if (_conversationId == null) {
      Logger.error('No conversation ID available for sending NSL question');
      return {
        'success': false,
        'message': 'No conversation ID available',
      };
    }

    final result = await runWithLoadingAndErrorHandling<Map<String, dynamic>>(
          () async {
            // Call the service to send the question with the enhanced message
            final response = await _chatApiService.sendNslQuestion(
                messageToSend, _conversationId!, actualUserId);

            // Reset lastUserMessageForApi after sending
            _lastUserMessageForApi = '';

            return _processApiResponse(response);
          },
          context: 'WebHomeProvider.sendNslQuestion',
        ) ??
        {'success': false, 'message': 'Failed to send NSL question'};

    return result;
  }

  // Fetch chat history
  Future<Map<String, dynamic>> fetchChatHistory([int? userId]) async {
    return await runWithLoadingAndErrorHandling<Map<String, dynamic>>(
          () async {
            // Get the user ID from AuthService if not provided
            String? userIdStr =
                userId?.toString() ?? await _authService.getUserId();
            String actualUserId = userIdStr ?? "1";

            Logger.info('Fetching chat history for user ID: $actualUserId');

            // Call the service to fetch chat history
            final response =
                await _chatApiService.fetchChatHistory(actualUserId);

            // Update the chat history with new API structure
            if (response['success'] && response['data']['sessions'] != null) {
              // Transform sessions to match expected format
              final sessions = response['data']['sessions'] as List<dynamic>;
              _chatHistory = sessions.map((session) {
                return {
                  'id': session['session_id'],
                  'session_id':
                      session['session_id'], // Keep both for compatibility
                  'title': session['title'],
                  'timestamp': session['last_active'],
                  'message_count': session['message_count'],
                };
              }).toList();
              notifyListeners();
            }

            return response;
          },
          context: 'WebHomeProvider.fetchChatHistory',
        ) ??
        {'success': false, 'message': 'Failed to fetch chat history'};
  }

  // Getter for chat history
  List<Map<String, dynamic>> get chatHistory => _chatHistory;

  // Get chat history
  Future<List<Map<String, dynamic>>> getChatHistory() async {
    try {
      // Get the user ID from AuthService
      final userId = await _authService.getUserId();

      if (userId == null) {
        Logger.error('User ID is null, cannot fetch chat history');
        return [];
      }

      // Fetch chat history using the existing method
      final response = await fetchChatHistory(int.tryParse(userId));

      if (response['success'] &&
          response['data'] != null &&
          response['data']['conversations'] != null) {
        // Return the chat history data
        return List<Map<String, dynamic>>.from(
            response['data']['conversations'] ?? []);
      } else {
        Logger.error('Error fetching chat history: ${response['message']}');
        return [];
      }
    } catch (e) {
      Logger.error('Exception fetching chat history: $e');
      return [];
    }
  }

  // Fetch chat history and update messages
  Future<void> fetchChatHistoryAndUpdateMessages() async {
    try {
      final chatHistory = await getChatHistory();

      if (chatHistory.isNotEmpty) {
        // Clear existing messages
        _messages.clear();

        // Add messages from chat history
        for (final chat in chatHistory) {
          // Add user message
          _messages.add(ChatMessage(
            content: chat['question'] ?? '',
            isUser: true,
            timestamp: DateTime.parse(
                chat['timestamp'] ?? DateTime.now().toIso8601String()),
          ));

          // Add AI response
          _messages.add(ChatMessage(
            content: chat['answer'] ?? '',
            isUser: false,
            timestamp: DateTime.parse(
                chat['timestamp'] ?? DateTime.now().toIso8601String()),
          ));
        }

        // Notify listeners of the changes
        notifyListeners();
      }
    } catch (e) {
      Logger.error('Error loading chat history: $e');
    }
  }

  // Create a new conversation session
  Future<Map<String, dynamic>> createNewConversationSession() async {
    return await runWithLoadingAndErrorHandling<Map<String, dynamic>>(
          () async {
            // Call the service to create a new conversation session
            final response =
                await _chatApiService.createNewConversationSession();

            // Store the session ID if successful
            if (response['success'] && response['session_id'] != null) {
              _currentSessionId = response['session_id'];
              Logger.info(
                  'Stored new conversation session ID: $_currentSessionId');

              // Save the session ID to SharedPreferences
              await _saveConversationState();
            }

            return response;
          },
          context: 'WebHomeProvider.createNewConversationSession',
        ) ??
        {
          'success': false,
          'message': 'Failed to create new conversation session'
        };
  }

  // Send user input to conversation API
  Future<Map<String, dynamic>> sendConversationInput(
      String sessionId, String userInput,
      {FileUploadOcrResponse? fileData}) async {
    return await runWithLoadingAndErrorHandling<Map<String, dynamic>>(
          () async {
            // Call the service to send user input to conversation API
            final response = await _chatApiService.sendConversationInput(
                sessionId, userInput,
                fileData: fileData);

            return response;
          },
          context: 'WebHomeProvider.sendConversationInput',
        ) ??
        {
          'success': false,
          'message': 'Failed to send user input to conversation API'
        };
  }

  // Load a specific conversation
  Future<Map<String, dynamic>> loadConversation(String conversationId,
      {int? userId}) async {
    // Get the user ID from AuthService if not provided
    String? userIdStr = userId?.toString() ?? await _authService.getUserId();
    String actualUserId = userIdStr ?? "1";

    Logger.info(
        'Loading conversation ID: $conversationId for user ID: $actualUserId');

    // Update the conversation ID
    _conversationId = conversationId;
    _isFirstMessage = false;

    // Save the conversation ID
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('general_conversation_id', _conversationId!);
      await prefs.setBool('is_first_message', false);
    } catch (e) {
      Logger.error('Error saving conversation state: $e');
    }

    // Fetch the chat history for this conversation
    final historyResult =
        await fetchConversationHistory(conversationId, actualUserId);

    // Notify listeners
    notifyListeners();

    return historyResult;
  }

  // Fetch chat history for a specific conversation
  Future<Map<String, dynamic>> fetchConversationHistory(String conversationId,
      [String? userId]) async {
    // Get the user ID from AuthService if not provided
    String? userIdStr = userId?.toString() ?? await _authService.getUserId();
    String actualUserId = userIdStr ?? "1";

    Logger.info(
        'Fetching conversation history for conversation ID: $conversationId and user ID: $actualUserId');

    return await runWithLoadingAndErrorHandling<Map<String, dynamic>>(
          () async {
            // Call the service to fetch conversation history
            final response = await _chatApiService.fetchConversationHistory(
                conversationId, actualUserId);

            return response;
          },
          context: 'WebHomeProvider.fetchConversationHistory',
        ) ??
        {
          'success': false,
          'message': 'Failed to fetch conversation history',
          'conversation_id': conversationId,
        };
  }

  // Create a new solution session for the first message in a session (streaming)
  Stream<Map<String, dynamic>> createSolutionSession(
      String initialInput) async* {
    solutionSessionModel = null;

    try {
      // Get tenant_id from user profile via AuthService
      final savedAuthData = await _authService.getSavedAuthData();
      final tenantId =
          savedAuthData.data?.user?.tenantId ?? 't001'; // Default fallback
      final userId = savedAuthData.data?.user?.id ?? 'user_001';

      // Keep project_id static for now as requested
      final projectId = "proj_${1000000 + math.Random().nextInt(1000)}";

      Logger.info(
          'Creating solution session with tenant_id: $tenantId, project_id: $projectId');

      // Call the streaming service to create solution session
      await for (final event in _chatApiService.createSolutionSession(
          tenantId, projectId, initialInput, userId)) {
        // Handle different event types
        if (event['success'] == true) {
          final eventType = event['event_type'] as String?;
          final eventData = event['data'] as Map<String, dynamic>?;

          Logger.info('Received solution session event: $eventType');

          // Handle completion event to store session data
          if (eventType == 'completion' && eventData != null) {
            final completionData = eventData['data'] as Map<String, dynamic>?;
            if (completionData != null) {
              final sessionId = completionData['session_id'] as String?;
              final conversationId =
                  completionData['conversation_id'] as String?;

              if (sessionId != null) {
                _currentSessionId = sessionId;
                _nodeId = conversationId;
                Logger.info(
                    'Stored new solution session ID: $_currentSessionId');

                // Save the session ID to SharedPreferences
                await _saveConversationState();
              }
            }
          }

          // Handle question event - extract question data for UI
          if (eventType == 'question' && eventData != null) {
            final questionData = eventData['data'] as Map<String, dynamic>?;
            if (questionData != null) {
              // Store question-specific data that UI might need
              final question = questionData['question'] as String?;
              final sessionId = questionData['session_id'] as String?;
              final conversationId = questionData['conversation_id'] as String?;

              if (sessionId != null) {
                _currentSessionId = sessionId;
              }
              if (conversationId != null) {
                _nodeId = conversationId;
              }

              Logger.info('Question received: $question');
              Logger.info(
                  'Session ID: $sessionId, Conversation ID: $conversationId');
            }
          }

          // Yield the event to the UI
          yield event;
        } else {
          // Handle error events
          Logger.error('Solution session error: ${event['message']}');
          yield event;
        }
      }
    } catch (e) {
      Logger.error('Error in createSolutionSession stream: $e');
      yield {
        'success': false,
        'message': 'Failed to create solution session: $e',
      };
    }
  }

  // Create a new solution message in a session (streaming)
  Stream<Map<String, dynamic>> createSolutionMessage(String input) async* {
    solutionSessionModel = null;

    try {
      final savedAuthData = await _authService.getSavedAuthData();
      final userId = savedAuthData.data?.user?.id ?? 'user_001';

      Logger.info('Creating solution message for session: $currentSessionId');

      // Call the streaming service to create solution message
      await for (final event in _chatApiService.createSolutionMessage(
          currentSessionId, input, _nodeId, userId)) {
        // Handle different event types
        if (event['success'] == true) {
          final eventType = event['event_type'] as String?;
          final eventData = event['data'] as Map<String, dynamic>?;

          Logger.info('Received solution message event: $eventType');

          // Handle completion event to store session data
          if (eventType == 'completion' && eventData != null) {
            final completionData = eventData['data'] as Map<String, dynamic>?;
            if (completionData != null) {
              final conversationId =
                  completionData['conversation_id'] as String?;

              if (conversationId != null) {
                _nodeId = conversationId;
                Logger.info('Updated conversation ID: $_nodeId');

                // Save the session ID to SharedPreferences
                await _saveConversationState();
              }
            }
          }

          // Handle question event - extract question data for UI
          if (eventType == 'question' && eventData != null) {
            final questionData = eventData['data'] as Map<String, dynamic>?;
            if (questionData != null) {
              // Store question-specific data that UI might need
              final question = questionData['question'] as String?;
              final sessionId = questionData['session_id'] as String?;
              final conversationId = questionData['conversation_id'] as String?;

              if (sessionId != null) {
                _currentSessionId = sessionId;
              }
              if (conversationId != null) {
                _nodeId = conversationId;
              }

              Logger.info('Question received: $question');
              Logger.info(
                  'Session ID: $sessionId, Conversation ID: $conversationId');

              // Create solution session model from question data if available
              try {
                solutionSessionModel =
                    SolutionSessionModel.fromJson(questionData);
              } catch (e) {
                Logger.error('Error parsing solution session model: $e');
              }
            }
          }

          // Yield the event to the UI
          yield event;
        } else {
          // Handle error events
          Logger.error('Solution message error: ${event['message']}');
          yield event;
        }
      }
    } catch (e) {
      Logger.error('Error in createSolutionMessage stream: $e');
      yield {
        'success': false,
        'message': 'Failed to create solution message: $e',
      };
    }
  }

  // Get solution status
  Future<bool> getSolutionStatus() async {
    return await runWithLoadingAndErrorHandling<bool>(
          () async {
            final savedAuthData = await _authService.getSavedAuthData();
            final userId = savedAuthData.data?.user?.id ?? 'user_001';
            final response = await _chatApiService.getSolutionStatus(
                currentSessionId, userId);
            solutionStatusModel =
                SolutionStatusModel.fromJson(response["data"]);
            _showStatus = true;
            return true;
          },
          context: 'WebHomeProvider.getSolutionStatus',
        ) ??
        false;
  }

  toggleStatus() {
    _showStatus = !_showStatus;
    notifyListeners();
  }

  setStatus() {
    _showStatus = true;
    notifyListeners();
  }

  toggleStatusArtifacts(SolutionSessionModel? brdDocument) {
    selectedSolutionSessionModel = brdDocument;
    _showStatusArtifacts = !_showStatusArtifacts;
    notifyListeners();
  }

  // Fetch available modes from API
  Future<void> fetchModes() async {
    _isLoadingModes = true;
    notifyListeners();

    try {
      Logger.info('Fetching available modes from API');

      final response = await _chatApiService.fetchModes();

      if (response['success'] && response['data'] != null) {
        final data = response['data'] as Map<String, dynamic>;
        final modes = data['modes'] as List<dynamic>?;

        if (modes != null) {
          _availableModes = modes.map((mode) => Mode.fromJson(mode)).toList();

          Logger.info('Successfully fetched ${_availableModes.length} modes');
          selectedQuickMessage = _availableModes[0].name;
        } else {
          Logger.error('No modes found in API response');
          _availableModes = [];
        }
      } else {
        Logger.error('Failed to fetch modes: ${response['message']}');
        _availableModes = [];
      }
    } catch (e) {
      Logger.error('Error fetching modes: $e');
      _availableModes = [];
    } finally {
      _isLoadingModes = false;
      notifyListeners();
    }
  }

  // Get quick messages list including modes and Solution
  List<String> getQuickMessages() {
    List<String> quickMessages = [];

    // Add modes from API
    for (Mode mode in _availableModes) {
      quickMessages.add(mode.name);
    }

    // Add "Solution" at the end
    quickMessages.add("Solution");
    selectedQuickMessage = quickMessages[0];

    return quickMessages;
  }

  // Get mode ID by name (for API calls)
  String? getModeIdByName(String name) {
    for (Mode mode in _availableModes) {
      if (mode.name == name) {
        return mode.id;
      }
    }
    return 'nsl_expert'; // Return null if not found (e.g., for "Solution")
  }

  // Get mode name by ID (for setting UI state from history)
  String? getModeNameById(String id) {
    for (Mode mode in _availableModes) {
      if (mode.id == id) {
        return mode.name;
      }
    }
    return null; // Return null if not found
  }

  // Set mode session for continuation (when loading from history)
  void setModeSessionForContinuation(String sessionId, String? modeId) {
    _modeSessionId = sessionId;
    _isFirstModeMessage = false; // Not the first message since we're continuing
    _selectedModeId = modeId;
    Logger.info(
        'Set mode session for continuation: sessionId=$sessionId, modeId=$modeId');
  }

  // Send a mode-based question to the API
  Future<Map<String, dynamic>> sendModeQuestion(String question,
      [String? userId]) async {
    // Get the user ID from AuthService if not provided
    String? userIdStr = userId?.toString() ?? await _authService.getUserId();
    String actualUserId = userIdStr ?? "default_user";

    // Use lastUserMessageForApi if available (for OCR text), otherwise use the original question
    String messageToSend =
        _lastUserMessageForApi.isNotEmpty ? _lastUserMessageForApi : question;

    // Log the message being sent
    if (_lastUserMessageForApi.isNotEmpty) {
      Logger.info('Using enhanced message with OCR text for API call');
      Logger.info('Original question: $question');
      Logger.info('Enhanced message: $messageToSend');
    }

    // Get the selected mode ID
    String? modeId = getModeIdByName(_selectedQuickMessage ?? '');
    if (modeId == null) {
      Logger.error(
          'No mode ID found for selected quick message: $_selectedQuickMessage');
      return {
        'success': false,
        'message': 'Invalid mode selected',
      };
    }

    Logger.info(
        'Sending mode question for user ID: $actualUserId with mode: $modeId');

    // If this is the first mode message, create a mode session first
    if (_isFirstModeMessage) {
      // Use the first message as the title (always use original question for title)
      String title = _createTitleFromMessage(question);

      final createResult = await createModeSession(actualUserId, title);

      if (!createResult['success']) {
        return createResult; // Return the error from create mode session
      }

      // Now we have a mode session ID, proceed with sending the question
      _isFirstModeMessage = false;
    }

    // Ensure we have a mode session ID
    if (_modeSessionId == null) {
      Logger.error('No mode session ID available for sending question');
      return {
        'success': false,
        'message': 'No mode session ID available',
      };
    }

    final result = await runWithLoadingAndErrorHandling<Map<String, dynamic>>(
          () async {
            // Call the service to send the mode chat with the enhanced message
            final response = await _chatApiService.sendModeChat(
                messageToSend, _modeSessionId!, modeId);

            // Reset lastUserMessageForApi after sending
            _lastUserMessageForApi = '';

            return response;
          },
          context: 'WebHomeProvider.sendModeQuestion',
        ) ??
        {'success': false, 'message': 'Failed to send mode question'};

    return result;
  }

  // Create a new mode session
  Future<Map<String, dynamic>> createModeSession(
      String userId, String title) async {
    Logger.info(
        'Creating mode session for user ID: $userId with title: $title');

    return await runWithLoadingAndErrorHandling<Map<String, dynamic>>(
          () async {
            // Call the service to create a mode session
            final response =
                await _chatApiService.createModeSession(userId, title);

            if (response['success']) {
              // Extract session ID from response
              final sessionId = response['session_id'];

              if (sessionId != null) {
                // Update state
                _modeSessionId = sessionId;
                Logger.info(
                    'Created new mode session with ID: $_modeSessionId');
              }
            }

            return response;
          },
          context: 'WebHomeProvider.createModeSession',
        ) ??
        {'success': false, 'message': 'Failed to create mode session'};
  }

  // Reset mode session state
  void resetModeSession() {
    _modeSessionId = null;
    _isFirstModeMessage = true;
    Logger.info('Reset mode session state');
  }

  sendSolutionQuestion(String text) async {
    final savedAuthData = await _authService.getSavedAuthData();
    final tenantId =
        savedAuthData.data?.user?.tenantId ?? 't001'; // Default fallback
    final userId = savedAuthData.data?.user?.id ?? 'user_001';

    // Keep project_id static for now as requested
    final projectId = "proj_${1000000 + math.Random().nextInt(1000)}";
    return await runWithLoadingAndErrorHandling<Map<String, dynamic>>(
          () async {
            // Call the service to create a mode session
            final response = await _chatApiService.startSolutionSession(
                tenantId, projectId, text, userId);

            if (response['success']) {
              // Extract session ID from response
              final sessionId = response['data']['conversation_id'];

              if (sessionId != null) {
                // Update state
                _currentSessionId = sessionId;
                //_nodeId = conversationId;
                Logger.info(
                    'Stored new solution session ID: $_currentSessionId');

                // Save the session ID to SharedPreferences
                await _saveConversationState();
                Logger.info(
                    'Created new mode session with ID: $_modeSessionId');
              }
            }

            return response;
          },
          context: 'WebHomeProvider.createModeSession',
        ) ??
        {'success': false, 'message': 'Failed to create mode session'};
  }

  sendSolutionQuestionContinue(String text) async {
    return await runWithLoadingAndErrorHandling<Map<String, dynamic>>(
          () async {
            // Call the service to create a mode session
            final response = await _chatApiService.continueSolutionSession(
                currentSessionId, text);

            if (response['success']) {
              // Extract session ID from response
              final sessionId = response['data']['conversation_id'];

              if (sessionId != null) {
                // Update state
                _currentSessionId = sessionId;
                //_nodeId = conversationId;
                Logger.info(
                    'Stored new solution session ID: $_currentSessionId');

                // Save the session ID to SharedPreferences
                await _saveConversationState();
                Logger.info(
                    'Created new mode session with ID: $_modeSessionId');
              }
            }

            return response;
          },
          context: 'WebHomeProvider.createModeSession',
        ) ??
        {'success': false, 'message': 'Failed to create mode session'};
  }

  // Fetch conversation from new API
  Future<Map<String, dynamic>> fetchConversationFromNewAPI(
      String projectId) async {
    return await runWithLoadingAndErrorHandling<Map<String, dynamic>>(
          () async {
            Logger.info('Fetching conversation for project ID: $projectId');

            // Call the service to fetch conversation
            final response =
                await _chatApiService.fetchConversationFromNewAPI(projectId);

            return response;
          },
          context: 'WebHomeProvider.fetchConversationFromNewAPI',
        ) ??
        {'success': false, 'message': 'Failed to fetch conversation'};
  }

  // Fetch messages for a specific conversation
  Future<Map<String, dynamic>> fetchConversationMessages(
      String conversationId) async {
    return await runWithLoadingAndErrorHandling<Map<String, dynamic>>(
          () async {
            Logger.info(
                'Fetching messages for conversation ID: $conversationId');

            // Call the service to fetch conversation messages
            final response = await _chatApiService
                .fetchConversationMessagesNew(conversationId);

            return response;
          },
          context: 'WebHomeProvider.fetchConversationMessagesNew',
        ) ??
        {'success': false, 'message': 'Failed to fetch conversation messages'};
  }

  // Fetch chat history
  Future<Map<String, dynamic>> fetchCreateChatHistory([int? userId]) async {
    return await runWithLoadingAndErrorHandling<Map<String, dynamic>>(
          () async {
            // Get the user ID from AuthService if not provided
            final savedAuthData = await _authService.getSavedAuthData();
            final tenantId = savedAuthData.data?.user?.tenantId ?? 't001';

            Logger.info('Fetching chat history for user tenant ID: $tenantId');

            // Call the service to fetch chat history
            final response =
                await _chatApiService.fetchCreateChatHistory(tenantId);

            // Update the chat history with new API structure
            if (response['success'] && response['data']['projects'] != null) {
              // Transform sessions to match expected format
              final sessions = response['data']['projects'] as List<dynamic>;
              _chatHistory = sessions.map((session) {
                return {
                  'id': session['project_id'],
                  'project_id':
                      session['project_id'], // Keep both for compatibility
                  'project_name': session['project_name'],
                  'timestamp': session['last_updated'],
                  'message_count': session['message_count'],
                };
              }).toList();
              notifyListeners();
            }

            return response;
          },
          context: 'WebHomeProvider.fetchChatHistory',
        ) ??
        {'success': false, 'message': 'Failed to fetch chat history'};
  }


}
