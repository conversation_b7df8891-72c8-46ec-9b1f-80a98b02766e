import 'package:flutter/material.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/nsl_tree_side_details_new.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';

class GoLoListPanel1 extends StatefulWidget {
  final String panelId;
  final String metricType; // 'GOs' or 'LOs'
  final VoidCallback onClose;
  final NslTreeSidePanel? nodeDetails; // API node details
  

  const GoLoListPanel1({
    super.key,
    required this.panelId,
    required this.metricType,
    required this.onClose,
    this.nodeDetails, // Add API data
  });

  @override
  State<GoLoListPanel1> createState() => _GoLoListPanelState();
}

class _GoLoListPanelState extends State<GoLoListPanel1> {
  // Static data for demonstration
   List<bool> _expandedStates = [];

  @override
  void initState() {
    super.initState();
    _initializeExpandedStates();
  }

  void _initializeExpandedStates() {
    if (widget.metricType == 'GOs' && widget.nodeDetails?.result?.gos != null) {
      _expandedStates = List.filled(widget.nodeDetails!.result!.gos!.length, false);
    } else if (widget.metricType == 'LOs' && widget.nodeDetails?.result?.gos != null) {
      // For LOs, we don't need expanded states since it's a flat list
      _expandedStates = [];
    } else {
      _expandedStates = [false, false, false]; // Default for static data
    }
  }

  @override
  Widget build(BuildContext context) {
    final panelTitle = widget.metricType == 'GOs' ? 'GO List' : 'LO List';

    return Container(
      width: MediaQuery.of(context).size.width * 0.16,
      height: MediaQuery.of(context).size.height,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          left: BorderSide(color: Color(0xFFD0D0D0),),
        ),
       
      ),
      child: Column(
        children: [
          _buildHeader(panelTitle),
          Expanded(
            child: _buildApiContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(String title) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal:AppSpacing.sm, vertical: AppSpacing.size10),
      decoration: BoxDecoration(
        color: Color(0XFFCBDDFF),
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200, width: 1),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s10,
                fontWeight: FontManager.semiBold,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          InkWell(
            onTap: widget.onClose,
            child: Container(
             // padding: EdgeInsets.all(4),
              child: Icon(
                Icons.close,
                size: 24,
                color: Colors.black
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildApiContent() {
    if (widget.metricType == 'GOs' && widget.nodeDetails?.result?.gos != null) {
      // Show GOs from API
      final gos = widget.nodeDetails!.result!.gos!;
      return SingleChildScrollView(
        child: Column(
          children: gos.asMap().entries.map((entry) {
            final index = entry.key;
            final go = entry.value;
            return Column(
              children: [
                _buildApiExpandableItem(go, index),
                if (index != gos.length - 1)
                  Divider(
                    height: 1,
                    color: Color(0xFFB4B4B4),
                  ),
              ],
            );
          }).toList(),
        ),
      );
    } 
    else if (widget.metricType.toLowerCase().contains('lo') && widget.nodeDetails?.result?.gos != null) {
      // Show all LOs from all GOs in a flat list
      return _buildAllLOsList();
    }
    else {
      // Fallback to static data when no API data
     // final data = staticData[widget.metricType] ?? [];
      return SizedBox();
      //_buildContent(data);
    }
  }

  Widget _buildApiExpandableItem(Go go, int index) {
    final isExpanded = index < _expandedStates.length ? _expandedStates[index] : false;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              setState(() {
                if (index < _expandedStates.length) {
                  _expandedStates[index] = !_expandedStates[index];
                }
              });
            },
            child: Container(
              margin: EdgeInsets.only(left:AppSpacing.md,top: AppSpacing.xs, bottom:AppSpacing.xs ,right:AppSpacing.sm),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                     '${index + 1}. ${go.name ?? 'GO Title'}',
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s12,
                        fontWeight: isExpanded ? FontManager.bold : FontManager.regular,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  Icon(
                    isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                    color: isExpanded ? Color(0xFF0058FF) : Colors.black,
                    size: 24,
                  ),
                ],
              ),
            ),
          ),
          
          if (isExpanded)
            Container(
              decoration: BoxDecoration(
                color: Color(0xFFF5F8FF),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(4),
                  bottomRight: Radius.circular(4),
                ),
              ),
              child: Column(
                children: [
                  Divider(height: 1, thickness: 1, color: Colors.grey.shade300),
                  // Show LOs under this GO
                  if (go.los != null && go.los!.isNotEmpty)
                    ...go.los!.asMap().entries.map((loEntry, ) {
                      final loIndex = loEntry.key;
                      final lo = loEntry.value;
                      return Column(
                        children: [
                          _buildApiSubItem(lo.name ?? 'LO Title', loIndex),
                          if (loIndex != go.los!.length - 1)
                            Divider(height: 1, thickness: 1, color: Colors.grey.shade300),
                        ],
                      );
                    }).toList()
                  else
                    _buildApiSubItem('No LOs available',0),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildApiSubItem(String title, int index) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: AppSpacing.xs, horizontal: AppSpacing.sm),
      child: Row(
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(left: AppSpacing.md),
              child: Text(
               '${index + 1}. $title',
                style: FontManager.getCustomStyle(
                  fontSize: FontManager.s12,
                  fontWeight: FontManager.regular,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
            ),
          ),
          Icon(
            Icons.keyboard_arrow_down,
            color: Colors.black,
            size: 24,
          ),
        ],
      ),
    );
  }

  // Build flat list of all LOs from all GOs
  Widget _buildAllLOsList() {
    final gos = widget.nodeDetails!.result!.gos!;
    List<Lo> allLos = [];
    
    // Collect all LOs from all GOs
    for (var go in gos) {
      if (go.los != null && go.los!.isNotEmpty) {
        allLos.addAll(go.los!);
      }
    }

    if (allLos.isEmpty) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(AppSpacing.lg),
          child: Text(
            'No LOs available',
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s12,
              fontWeight: FontManager.regular,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey.shade600,
            ),
          ),
        ),
      );
    }

    return SingleChildScrollView(
      child: Column(
        children: allLos.asMap().entries.map((entry) {
          final index = entry.key;
          final lo = entry.value;
          return Column(
            children: [
              Container(
                margin: EdgeInsets.only(left: AppSpacing.md, top: AppSpacing.xs, bottom: AppSpacing.xs, right: AppSpacing.sm),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        '${index + 1}. ${lo.name ?? 'LO Title'}',
                        style: FontManager.getCustomStyle(
                          fontSize: FontManager.s12,
                          fontWeight: FontManager.regular,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              if (index != allLos.length - 1)
                Divider(
                  height: 1,
                  color: Color(0xFFB4B4B4),
                ),
            ],
          );
        }).toList(),
      ),
    );
  }

 Widget _buildContent(List<Map<String, String>> data) {
  return SingleChildScrollView(
    child: Column(
      children: data.asMap().entries.map((entry) {
        final index = entry.key;
        final item = entry.value;
        return Column(
          children: [
            _buildExpandableItem(item, index),
            // Add divider after each item except the last one
            if (index != data.length )
              Divider(
                height: 1,
               
                color: Color(0xFFB4B4B4),
              ),],);
          
        }).toList(),
      ),
    );
  }

  Widget _buildExpandableItem(Map<String, String> item, int index) {
    final isExpanded = index < _expandedStates.length ? _expandedStates[index] : false;

    return Container(
   //   margin: EdgeInsets.only(left:AppSpacing.md,top: AppSpacing.xs, bottom:AppSpacing.xs ,right:AppSpacing.sm),
      decoration: BoxDecoration(
        color: Colors.white,
   
      //  borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              setState(() {
                if (index < _expandedStates.length) {
                  _expandedStates[index] = !_expandedStates[index];
                }
              });
            },
            child: Container(
              margin: EdgeInsets.only(left:AppSpacing.md,top: AppSpacing.xs, bottom:AppSpacing.xs ,right:AppSpacing.sm),
             // padding: EdgeInsets.all(AppSpacing.md),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                   '${index + 1}. ${item['title'] ?? ''}',
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s12,
                        fontWeight: isExpanded?FontManager.bold:  FontManager.regular,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  Icon(
                    isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                    color:isExpanded ? Color(0xFF0058FF):  Colors.black,
                    size: 24,
                  ),
                ],
              ),
            ),
          ),
          
          if (isExpanded)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Expanded content
        if (isExpanded)
          Container(
           // margin: EdgeInsets.only(left:AppSpacing.lg,top: AppSpacing.xs, bottom:AppSpacing.xs ,right:AppSpacing.sm),
            // padding: EdgeInsets.only(
            //   left: AppSpacing.md,
            //   right: AppSpacing.md,
            //   bottom: AppSpacing.md,
            // ),
            decoration: BoxDecoration(
              color: Color(0xFFF5F8FF),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(4),
                bottomRight: Radius.circular(4),
              ),
            ),
            child:  Column(
              children: List.generate(8, (subIndex) => Column(
                children: [
                  Divider(height: 1, thickness: 1, color: Colors.grey.shade300),
                  _buildSubItem("LO Title", true, subIndex),
                ],
              )),
            ),
          ),
      ],
    ),
  ] ),);
}
  
 // Helper widget for sub-items with checkmarks
Widget _buildSubItem(String title, bool isChecked, int index) {
  return Padding(
    padding: EdgeInsets.symmetric(vertical: AppSpacing.xs,horizontal: AppSpacing.sm),
    child: Row(
      children: [
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(left:AppSpacing.md),
            child: Text(
              '${index + 1}. $title',
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontManager.regular,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
        ),
        if (isChecked)
          Icon(
           Icons.keyboard_arrow_down,
            color: Colors.black,
            size: 24,
          ),
      ],
    ),
  );
}
}

class GoLoPanel {
  final String id;
  final String metricType;

  GoLoPanel({
    required this.id,
    required this.metricType,
  });
}
