<#
    🛠 FLUTTER WEB BUILD SCRIPT (PowerShell Version)

    📌 Description:
        - Increments the patch and build version in `pubspec.yaml`
        - Builds the Flutter web app
        - Appends version string to JS and manifest.json URLs for cache busting
        - Updates <base href> in `index.html`

    ✅ Works In:
        - Windows PowerShell (5.1+)
        - PowerShell Core (7+)
        - Windows CMD (via PowerShell command)
        - Visual Studio Code terminal (PowerShell mode)

    🚀 How to Run This Script:

    ▸ From PowerShell (open in the project root):
        Set-ExecutionPolicy Bypass -Scope Process -Force
        .\build_flutter_web.ps1

    ▸ From CMD (Command Prompt):
        powershell -ExecutionPolicy Bypass -File build_flutter_web.ps1

    ▸ From VS Code Terminal (PowerShell selected):
        .\build_flutter_web.ps1

    🧰 Requirements:
        - Flutter SDK must be installed and added to system PATH
        - PowerShell (comes pre-installed on Windows)
        - This script must be placed in the project root (next to pubspec.yaml)
#>

Write-Host "🔄 Reading and incrementing version from pubspec.yaml..."

# Read version line
$pubspec = Get-Content "pubspec.yaml"
$versionLineIndex = ($pubspec | Select-String '^version:' | Select-Object -First 1).LineNumber - 1
$currentVersion = $pubspec[$versionLineIndex] -replace 'version:\s*', ''
$versionParts = $currentVersion -split '\+'
$semantic = $versionParts[0]
$build = [int]$versionParts[1]

$semParts = $semantic -split '\.'
$major = $semParts[0]
$minor = $semParts[1]
$patch = [int]$semParts[2]

# Increment patch and build
$newPatch = $patch + 1
$newBuild = $build + 1
$newVersion = "$major.$minor.$newPatch+$newBuild"
$versionNoPlus = "$major.$minor.$newPatch$newBuild"

Write-Host "📦 New version: $newVersion"

# Replace version in pubspec.yaml
$pubspec[$versionLineIndex] = "version: $newVersion"
Set-Content -Path "pubspec.yaml" -Value $pubspec

Write-Host "🧹 Running flutter clean packages get..."
flutter clean
flutter packages get

Write-Host "🚀 Building Flutter web..."
flutter build web --release

# Update base href in index.html
Write-Host "🔗 Updating base href in index.html"
$indexPath = "build\web\index.html"
(Get-Content $indexPath) -replace '<base href="/"', '<base href="/nsl/nsl_new/"' | Set-Content $indexPath

# Patch JS file references
Write-Host "🔧 Patching JS references with version..."
$filesToPatch = @(
    "build\web\flutter.js",
    "build\web\flutter_bootstrap.js",
    "build\web\index.html"
)

foreach ($file in $filesToPatch) {
    if (Test-Path $file) {
        (Get-Content $file) -replace '"main.dart.js"', '"main.dart.js?v=' + $versionNoPlus + '"' | Set-Content $file
    }
}

# Patch asset loader in main.dart.js
Write-Host "🛠 Patching asset loader in main.dart.js"
$mainJs = "build\web\main.dart.js"
if (Test-Path $mainJs) {
    (Get-Content $mainJs) -replace 'self\.window\.fetch\(a\),', 'self.window.fetch(a + ''?v=' + $versionNoPlus + '''),' | Set-Content $mainJs
}

# Add version to manifest.json
Write-Host "📦 Appending version to manifest.json URL"
if (Test-Path $indexPath) {
    (Get-Content $indexPath) -replace '"manifest.json"', "`"manifest.json?v=$versionNoPlus`"" | Set-Content $indexPath
}

Write-Host "✅ Flutter web build and patching completed successfully!"
