// Custom ExpansionTile that separates title tap from expansion toggle
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:provider/provider.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/accordion_controller.dart';

class CustomExpansionTileNew extends StatefulWidget {
  final Widget title;
  final List<Widget> children;
  final bool initiallyExpanded;
  final Function(bool) onExpansionChanged;
  final VoidCallback onTitleTap;
  final VoidCallback? onThreeDotsPressed;
  final Color backgroundColor;
  final bool showArrow;
  final bool showThreeDots;
  final String? panelId; // Unique identifier for accordion behavior
  final AccordionController?
      accordionController; // Controller for accordion behavior

  const CustomExpansionTileNew({
    super.key,
    required this.title,
    required this.children,
    this.initiallyExpanded = false,
    required this.onExpansionChanged,
    required this.onTitleTap,
    this.onThreeDotsPressed,
    this.backgroundColor = Colors.white,
    this.showArrow = true,
    this.showThreeDots = true,
    this.panelId,
    this.accordionController,
  });

  @override
  State<CustomExpansionTileNew> createState() => CustomExpansionTileNewState();
}

class CustomExpansionTileNewState extends State<CustomExpansionTileNew>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _heightFactor;
  late Animation<double> _iconTurn;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(milliseconds: 200),
      vsync: this,
    );
    _heightFactor = _controller.drive(CurveTween(curve: Curves.easeIn));
    _iconTurn = _controller.drive(Tween<double>(begin: 0.0, end: 0.5)
        .chain(CurveTween(curve: Curves.easeIn)));

    // Set initial expansion state
    if (widget.accordionController != null && widget.panelId != null) {
      _isExpanded =
          widget.accordionController!.isPanelExpanded(widget.panelId!);
    } else {
      _isExpanded = widget.initiallyExpanded;
    }

    if (_isExpanded) {
      _controller.value = 1.0;
    }

    // Listen to accordion controller changes
    if (widget.accordionController != null && widget.panelId != null) {
      widget.accordionController!.addListener(_onAccordionStateChanged);
    }
  }

  @override
  void didUpdateWidget(CustomExpansionTileNew oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.initiallyExpanded != oldWidget.initiallyExpanded) {
      if (widget.initiallyExpanded) {
        _isExpanded = true;
        _controller.forward();
      } else {
        _isExpanded = false;
        _controller.reverse();
      }
    }
  }

  void _onAccordionStateChanged() {
    if (widget.accordionController != null && widget.panelId != null) {
      final shouldBeExpanded =
          widget.accordionController!.isPanelExpanded(widget.panelId!);
      if (shouldBeExpanded != _isExpanded) {
        setState(() {
          _isExpanded = shouldBeExpanded;
          if (_isExpanded) {
            _controller.forward();
          } else {
            _controller.reverse();
          }
          widget.onExpansionChanged(_isExpanded);
        });
      }
    }
  }

  @override
  void dispose() {
    // Remove accordion controller listener
    if (widget.accordionController != null && widget.panelId != null) {
      widget.accordionController!.removeListener(_onAccordionStateChanged);
    }
    _controller.dispose();
    super.dispose();
  }

  void _toggleExpansion() {
    // Only allow expansion if arrow is shown
    if (!widget.showArrow) return;

    // If accordion controller is provided and panelId is set, use accordion behavior
    if (widget.accordionController != null && widget.panelId != null) {
      widget.accordionController!.togglePanel(widget.panelId!);
      return;
    }

    // Default behavior (non-accordion)
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
      widget.onExpansionChanged(_isExpanded);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: _isExpanded ? Color(0xffE4EDFF) : widget.backgroundColor,
      child: Column(
        children: [
          // Title row with conditional tap handlers
          InkWell(
            onTap: () {
              // Always call onTitleTap
              widget.onTitleTap();
              // Only toggle expansion if arrow is shown
              if (widget.showArrow) {
                _toggleExpansion();
              }
            },
            child: Container(
              color: Colors
                  .transparent, // Make it transparent to let parent color show through
              padding: EdgeInsets.symmetric(
                horizontal: AppSpacing.xs,
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(child: widget.title),
                  // Conditionally show arrow icon
                  if (widget.showArrow)
                    Padding(
                      padding: EdgeInsets.symmetric(vertical: 7.0),
                      child: HoverArrowIcon(
                        onTap: _toggleExpansion,
                        iconTurn: _iconTurn,
                      ),
                    ),
                  // HoverArrowIcon(
                  //   onTap: _toggleExpansion,
                  //   iconTurn: _iconTurn,
                  // ),
                  // Static toggle menu after down arrow
                  if (widget.showArrow) StaticToggleMenu(),
                  // Always show three dots icon
                  widget.showThreeDots
                      ? HoverThreeDotsIcon(
                          onTap: widget.onThreeDotsPressed ?? () {},
                        )
                      : SizedBox(),
                ],
              ),
            ),
          ),
          // Expandable content
          AnimatedBuilder(
            animation: _controller.view,
            builder: (context, child) {
              return ClipRect(
                child: Align(
                  heightFactor: _heightFactor.value,
                  alignment:
                      Alignment.topLeft, // Align content to the start (left)
                  // Align content to the start (left)
                  child: child,
                ),
              );
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: widget.children,
            ),
          ),
        ],
      ),
    );
  }
}

class HoverArrowIcon extends StatefulWidget {
  final VoidCallback onTap;
  final Animation<double> iconTurn;

  const HoverArrowIcon({
    super.key,
    required this.onTap,
    required this.iconTurn,
  });

  @override
  State<HoverArrowIcon> createState() => _HoverArrowIconState();
}

class _HoverArrowIconState extends State<HoverArrowIcon> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: InkWell(
        onTap: widget.onTap,
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        splashFactory: NoSplash.splashFactory,
        child: Padding(
          padding: const EdgeInsets.all(4.0),
          child: RotationTransition(
            turns: widget.iconTurn,
            child: Icon(
              Icons.keyboard_arrow_down,
              size: 20,
              color: isHovered ? Color(0xff0058FF) : Colors.grey.shade800,
            ),
          ),
        ),
      ),
    );
  }
}

class HoverThreeDotsIcon extends StatefulWidget {
  final VoidCallback onTap;

  const HoverThreeDotsIcon({
    super.key,
    required this.onTap,
  });

  @override
  State<HoverThreeDotsIcon> createState() => _HoverThreeDotsIconState();
}

class _HoverThreeDotsIconState extends State<HoverThreeDotsIcon> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: InkWell(
        onTap: widget.onTap,
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        splashFactory: NoSplash.splashFactory,
        child: Padding(
          padding: const EdgeInsets.all(4.0),
          child: Icon(
            Icons.more_vert,
            size: 20,
            color: isHovered ? Color(0xff0058FF) : Colors.grey.shade800,
          ),
        ),
      ),
    );
  }
}

// Provider for managing toggle menu state
class ToggleMenuProvider extends ChangeNotifier {
  bool _isHovered = false;
  bool _isMenuOpen = false;

  bool get isHovered => _isHovered;
  bool get isMenuOpen => _isMenuOpen;

  void setHovered(bool hovered) {
    if (_isHovered != hovered) {
      _isHovered = hovered;
      notifyListeners();
    }
  }

  void setMenuOpen(bool open) {
    if (_isMenuOpen != open) {
      _isMenuOpen = open;
      notifyListeners();
    }
  }

  void handleMenuSelection(String value) {
    // Handle menu selection logic here
    // This can be extended to handle different menu actions
    setMenuOpen(false);
    notifyListeners();
  }
}

class StaticToggleMenu extends StatelessWidget {
  const StaticToggleMenu({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => ToggleMenuProvider(),
      child: Consumer<ToggleMenuProvider>(
        builder: (context, provider, child) {
          return MouseRegion(
            onEnter: (_) => provider.setHovered(true),
            onExit: (_) => provider.setHovered(false),
            child: InkWell(
              onTap: () {},
              hoverColor: Colors.transparent,
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
              splashFactory: NoSplash.splashFactory,
              child: Center(
                child: Container(
                  height: 28,
                  width: 28,
                  margin: EdgeInsets.only(top: 8),
                  decoration: BoxDecoration(
                    color: provider.isMenuOpen
                        ? Color(0xff0058FF)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(6.0),
                  ),
                  child: Center(
                    child: PopupMenuButton<String>(
                      tooltip: '',
                      icon: Icon(
                        Icons.more_vert,
                        size: 20,
                        color: provider.isMenuOpen
                            ? Colors.white
                            : provider.isHovered
                                ? Color(0xff0058FF)
                                : Colors.grey.shade800,
                      ),
                      onSelected: (String value) {
                        provider.handleMenuSelection(value);
                      },
                      onOpened: () {
                        provider.setMenuOpen(true);
                      },
                      onCanceled: () {
                        provider.setMenuOpen(false);
                      },
                      constraints: BoxConstraints(
                        minWidth: 100,
                        maxWidth: 120,
                      ),
                      itemBuilder: (BuildContext context) =>
                          <PopupMenuEntry<String>>[
                        PopupMenuItem<String>(
                          value: 'nested',
                          height: 28,
                          padding:
                              EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SvgPicture.asset(
                                'assets/images/entity/nested.svg',
                                width: 16,
                                height: 16,
                                colorFilter: ColorFilter.mode(
                                  Colors.grey.shade700,
                                  BlendMode.srcIn,
                                ),
                              ),
                              SizedBox(width: 4),
                              Flexible(
                                child: Text(
                                  'Nested',
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.bodySmall(context),
                                    color: Colors.black,
                                    fontWeight: FontManager.regular,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                        PopupMenuItem<String>(
                          value: 'shared',
                          height: 28,
                          padding:
                              EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SvgPicture.asset(
                                'assets/images/entity/shared.svg',
                                width: 16,
                                height: 16,
                                colorFilter: ColorFilter.mode(
                                  Colors.grey.shade700,
                                  BlendMode.srcIn,
                                ),
                              ),
                              SizedBox(width: 4),
                              Flexible(
                                child: Text(
                                  'Shared',
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.bodySmall(context),
                                    color: Colors.black,
                                    fontWeight: FontManager.regular,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                        PopupMenuItem<String>(
                          value: 'junction',
                          height: 28,
                          padding:
                              EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SvgPicture.asset(
                                'assets/images/entity/junction.svg',
                                width: 16,
                                height: 16,
                                colorFilter: ColorFilter.mode(
                                  Colors.grey.shade700,
                                  BlendMode.srcIn,
                                ),
                              ),
                              SizedBox(width: 4),
                              Flexible(
                                child: Text(
                                  'Junction',
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.bodySmall(context),
                                    color: Colors.black,
                                    fontWeight: FontManager.regular,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                        PopupMenuItem<String>(
                          value: 'agents',
                          height: 28,
                          padding:
                              EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SvgPicture.asset(
                                'assets/images/entity/agent.svg',
                                width: 16,
                                height: 16,
                                colorFilter: ColorFilter.mode(
                                  Colors.grey.shade700,
                                  BlendMode.srcIn,
                                ),
                              ),
                              SizedBox(width: 4),
                              Flexible(
                                child: Text(
                                  'Agents',
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.bodySmall(context),
                                    color: Colors.black,
                                    fontWeight: FontManager.regular,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                        PopupMenuItem<String>(
                          value: 'workflows',
                          height: 28,
                          padding:
                              EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SvgPicture.asset(
                                'assets/images/entity/workflow.svg',
                                width: 16,
                                height: 16,
                                colorFilter: ColorFilter.mode(
                                  Colors.grey.shade700,
                                  BlendMode.srcIn,
                                ),
                              ),
                              SizedBox(width: 4),
                              Flexible(
                                child: Text(
                                  'Workflows',
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.bodySmall(context),
                                    color: Colors.black,
                                    fontWeight: FontManager.regular,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                      offset: const Offset(0, 35),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                        side: BorderSide(
                          color: Color(0xFF0058FF),
                          width: 0.5,
                        ),
                      ),
                      elevation: 8,
                      color: Colors.white,
                      splashRadius: 20,
                      padding: EdgeInsets.symmetric(vertical: 4),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
