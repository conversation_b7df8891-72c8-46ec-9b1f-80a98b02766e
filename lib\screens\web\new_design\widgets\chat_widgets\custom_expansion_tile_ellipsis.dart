// Custom expansion tile that handles ellipsis correctly
import 'package:flutter/material.dart';
import 'package:nsl/models/entities_data.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/custom_expansion_tile_new.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/text_overflow_detector.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/entity_profile_card.dart'
    as entity_card;
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/accordion_controller.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/logger.dart';
import 'package:nsl/widgets/custom_checkbox.dart';
import 'package:nsl/utils/mobile_tooltip_utils.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/utils/font_manager.dart';

class CustomExpansionTileWithEllipsis extends StatefulWidget {
  final Entity entity;
  final GlobalKey entityCardKey;
  final Function(bool) onExpansionChanged;
  final VoidCallback onTitleTap;
  final VoidCallback? onThreeDotsPressed;
  final Color backgroundColor;
  final List<Widget> children;
  final bool? showThreeDots;
  final AccordionController?
      accordionController; // Controller for accordion behavior

  const CustomExpansionTileWithEllipsis({
    super.key,
    required this.entity,
    required this.entityCardKey,
    required this.onExpansionChanged,
    required this.onTitleTap,
    this.onThreeDotsPressed,
    this.backgroundColor = Colors.transparent,
    this.children = const [],
    this.showThreeDots,
    this.accordionController,
  });

  @override
  State<CustomExpansionTileWithEllipsis> createState() =>
      CustomExpansionTileWithEllipsisState();
}

class CustomExpansionTileWithEllipsisState
    extends State<CustomExpansionTileWithEllipsis> {
  late bool isExpanded;

  // Add overlay management for entity hover cards
  OverlayEntry? _profileTooltipOverlay;
  DateTime _lastClickTime = DateTime.now();

  @override
  void initState() {
    super.initState();
    isExpanded = widget.entity.expanded ?? false;
  }

  @override
  void didUpdateWidget(CustomExpansionTileWithEllipsis oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.entity.expanded != widget.entity.expanded) {
      isExpanded = widget.entity.expanded ?? false;
    }
  }

  @override
  void dispose() {
    _hideEntityProfileTooltip(); // Clean up any active tooltips
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Log the expansion state for debugging
    Logger.info(
        "Building CustomExpansionTile with expanded: $isExpanded for entity: ${widget.entity.title}");

    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate available width for text (accounting for padding, potential arrow space, and three dots space)
        final double availableWidth = constraints.maxWidth -
            (AppSpacing.xs * 2) -
            60; // 30px for arrow + 30px for three dots

        // Check if text would overflow using simplified approach
        final bool wouldOverflow = _wouldTextOverflow(availableWidth);

        Logger.info(
            "Text overflow check for ${widget.entity.title}: wouldOverflow=$wouldOverflow, availableWidth=$availableWidth");

        return CustomExpansionTileNew(
          title: Builder(
            builder: (context) {
              // Show with ellipsis when collapsed, without ellipsis when expanded
              return _buildEntityTitle(
                widget.entityCardKey,
                widget.entity,
                isExpanded,
              );
            },
          ),
          initiallyExpanded: isExpanded,
          onExpansionChanged: (expanded) {
            setState(() {
              isExpanded = expanded;
            });
            widget.onExpansionChanged(expanded);
          },
          onTitleTap: widget.onTitleTap,
          onThreeDotsPressed: widget.onThreeDotsPressed,
          backgroundColor: widget.backgroundColor,
          showArrow: wouldOverflow, // Only show arrow if text would overflow
          children: widget.children,
          showThreeDots: widget.showThreeDots ?? true,
          panelId: widget.entity.id, // Use entity ID as panel ID
          accordionController: widget.accordionController,
        );
      },
    );
  }

  // Check if the text would overflow in the available width (simplified approach)
  bool _wouldTextOverflow(double availableWidth) {
    if (availableWidth <= 0) return false;

    // Create a simplified text string for measurement (without WidgetSpans)
    String fullText = widget.entity.title ?? 'Untitled';
    if (widget.entity.attributeString != null &&
        widget.entity.attributeString!.isNotEmpty) {
      fullText += ' has ${widget.entity.attributeString}';
    }

    // Use simple text overflow detection
    return TextOverflowDetector.wouldTextOverflow(
      text: fullText,
      style: FontManager.getCustomStyle(
        fontWeight: FontManager.semiBold,
        fontSize: ResponsiveFontSizes.bodyMedium(context),
        fontFamily: FontManager.fontFamilyTiemposText,
      ),
      maxWidth: availableWidth,
      maxLines: 1,
    );
  }

  // Build entity title with or without ellipsis
  Widget _buildEntityTitle(
      GlobalKey entityCardKey, Entity entity, bool expanded) {
    return Row(
      key: entityCardKey,
      children: [
        Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: AppSpacing.xs),
            child: Row(
              children: [
                // Create a combined RichText for the entire title with attributes
                Expanded(
                  child: Builder(
                    builder: (context) {
                      final GlobalKey titleKey =
                          GlobalKey(debugLabel: 'entityTitle_${entity.id}');

                      // Create a list of text spans for the combined title
                      List<InlineSpan> titleSpans = [];

                      // Add the entity title with bold style
                      TextSpan titleSpan = TextSpan(
                        text: entity.title ?? 'Untitled',
                        style: TextStyle(
                          fontWeight: FontManager.semiBold,
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontFamily: FontManager.fontFamilyTiemposText,
                        ),
                      );

                      // Wrap title with responsive tooltip (mobile + web)
                      titleSpans.add(WidgetSpan(
                        child: _buildEntityTitleWithTooltip(
                          context,
                          titleSpan,
                          entity,
                        ),
                      ));

                      // Add attributes if they exist
                      if (entity.attributeString != null &&
                          entity.attributeString!.isNotEmpty) {
                        // Add " has " text
                        titleSpans.add(
                          TextSpan(
                            text: ' has ',
                            style: FontManager.getCustomStyle(
                              fontWeight: FontManager.medium,
                              color: Colors.grey.shade700,
                              fontFamily: FontManager.fontFamilyTiemposText,
                            ),
                          ),
                        );

                        // Add attribute spans (simplified to avoid WidgetSpan issues)
                        titleSpans.addAll(_buildSimplifiedAttributeSpans(
                          entity.attributeString ?? '',
                          entity.attributes ?? [],
                        ));
                      }

                      // Log the RichText properties for debugging
                      Logger.info(
                          "RichText with expanded: $expanded, overflow: ${!expanded ? 'ellipsis' : 'visible'}, maxLines: ${!expanded ? 1 : 'null'}");

                      return RichText(
                        key: titleKey,
                        text: TextSpan(
                            children: titleSpans,
                            style: TextStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: FontManager.medium,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              height: 2,
                              textBaseline: TextBaseline.alphabetic,
                            )),
                        overflow: !expanded
                            ? TextOverflow.ellipsis
                            : TextOverflow.visible,
                        softWrap: expanded,
                        maxLines: !expanded ? 1 : null,
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Build simplified attribute spans (avoiding WidgetSpan for PK indicators in overflow detection)
  List<InlineSpan> _buildSimplifiedAttributeSpans(
      String attributeString, List<Attribute> attributes) {
    // Log for debugging
    Logger.info("Building simplified attribute spans for: $attributeString");

    if (attributeString.isEmpty) {
      return [];
    }

    // Create a map of attribute names to their isPk status
    final Map<String, bool> primaryKeyMap = {};
    for (var attribute in attributes) {
      if (attribute.name != null) {
        primaryKeyMap[attribute.name!] = attribute.isPk ?? false;
      }
    }

    final Map<String, bool> foreignKeyMap = {};
    for (var attribute in attributes) {
      if (attribute.name != null) {
        foreignKeyMap[attribute.name!] = attribute.isFk ?? false;
      }
    }

    // Split the attribute string by commas and "and"
    List<String> parts = [];
    final andParts = attributeString.split(' and ');
    for (int i = 0; i < andParts.length; i++) {
      if (i < andParts.length - 1) {
        final commaParts = andParts[i].split(', ');
        parts.addAll(commaParts);
      } else {
        parts.add(andParts[i]);
      }
    }

    // Create a list of text spans for each attribute
    List<InlineSpan> textSpans = [];
    for (int i = 0; i < parts.length; i++) {
      String part = parts[i].trim();
      bool isPrimaryKey = false;
      bool isForeignKey = false;

      for (var attrName in primaryKeyMap.keys) {
        if (part.contains(attrName) && primaryKeyMap[attrName] == true) {
          isPrimaryKey = true;
          break;
        }
      }

      for (var attrName in foreignKeyMap.keys) {
        if (part.contains(attrName) && foreignKeyMap[attrName] == true) {
          isForeignKey = true;
          break;
        }
      }

      // Add the attribute with or without PK indicator
      if (isPrimaryKey) {
        textSpans.add(
          TextSpan(
            text: part,
            style: FontManager.getCustomStyle(
              fontWeight: FontManager.semiBold,
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              color: Colors.black,
              fontFamily: FontManager.fontFamilyTiemposText,
            ),
          ),
        );
        textSpans.add(TextSpan(text: ' '));
        textSpans.add(
          WidgetSpan(
            alignment: PlaceholderAlignment.top,
            child: Transform.translate(
              offset: Offset(0, -5),
              child: Text(
                '^PK',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelSmall(context),
                  fontWeight: FontManager.semiBold,
                  color: Color(0xff0058FF),
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
            ),
          ),
        );
      } else if (isForeignKey) {
        textSpans.add(
          TextSpan(
            text: part,
            style: FontManager.getCustomStyle(
              fontWeight: FontManager.semiBold,
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              color: Colors.black,
              fontFamily: FontManager.fontFamilyTiemposText,
            ),
          ),
        );
        textSpans.add(TextSpan(text: ' '));
        textSpans.add(
          WidgetSpan(
            alignment: PlaceholderAlignment.top,
            child: Transform.translate(
              offset: Offset(0, -5),
              child: Text(
                '^FK',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelSmall(context),
                  fontWeight: FontManager.semiBold,
                  color: Color(0xff0058FF),
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
            ),
          ),
        );
      } else {
        textSpans.add(
          TextSpan(
            text: part,
            style: FontManager.getCustomStyle(
              fontWeight: FontManager.semiBold,
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              color: Colors.black,
              fontFamily: FontManager.fontFamilyTiemposText,
            ),
          ),
        );
      }

      // Add separator if not the last item
      if (i < parts.length - 1) {
        if (i == parts.length - 2) {
          textSpans.add(TextSpan(text: ' and '));
        } else {
          textSpans.add(TextSpan(text: ', '));
        }
      }
    }

    return textSpans;
  }

  /// Builds entity title with responsive tooltip (mobile long press + web hover)
  Widget _buildEntityTitleWithTooltip(
    BuildContext context,
    TextSpan titleSpan,
    Entity entity,
  ) {
    final tooltipContent = entity_card.EntityProfileCard(
      entity: entity,
      width: MobileTooltipUtils.isMobile(context) ? double.infinity : 320.0,
      leftMargin: MobileTooltipUtils.isMobile(context) ? 0 : 0,
    );

    final titleText = Text.rich(
      style: TextStyle(
        fontSize: ResponsiveFontSizes.bodyMedium(context),
        fontFamily: FontManager.fontFamilyTiemposText,
        height: 1.2,
        textBaseline: TextBaseline.alphabetic,
      ),
      TextSpan(text: titleSpan.text, style: titleSpan.style),
    );

    return MobileTooltipUtils.createResponsiveTooltipWrapper(
      context: context,
      child: titleText,
      tooltipContent: tooltipContent,
      webTooltipChild: Builder(
        builder: (context) {
          final titleKey = GlobalKey();
          return MouseRegion(
            key: titleKey,
            cursor: SystemMouseCursors.click,
            onEnter: (_) {
              _showEntityProfileTooltip(titleKey, entity);
            },
            onExit: (_) {
              _hideEntityProfileTooltip();
            },
            child: titleText,
          );
        },
      ),
    );
  }

  // Show entity profile tooltip
  void _showEntityProfileTooltip(GlobalKey titleKey, Entity entity) {
    // Don't show tooltip if the user is clicking
    if (DateTime.now().difference(_lastClickTime).inMilliseconds < 300) {
      return;
    }

    // Hide any existing tooltips first
    _hideEntityProfileTooltip();

    // Get the position of the entity title
    final RenderBox? renderBox =
        titleKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final position = renderBox.localToGlobal(Offset.zero);
    final screenSize = MediaQuery.of(context).size;

    // Calculate tooltip dimensions
    const double tooltipWidth = 320.0;
    const double tooltipMaxHeight = 300.0;

    // Calculate position to ensure tooltip stays within screen bounds
    double left = position.dx;
    double top =
        position.dy + 30; // Position below the entity title with some spacing

    // Adjust horizontal position if tooltip would go off-screen
    if (left + tooltipWidth > screenSize.width - 20) {
      left = screenSize.width - tooltipWidth - 20;
    }
    if (left < 20) {
      left = 20;
    }

    // Adjust vertical position if tooltip would go off-screen
    if (top + tooltipMaxHeight > screenSize.height - 20) {
      top = position.dy -
          tooltipMaxHeight -
          10; // Position above the entity title
    }
    if (top < 20) {
      top = 20;
    }

    // Create the overlay entry
    _profileTooltipOverlay = OverlayEntry(
      builder: (context) => Positioned(
        left: left,
        top: top,
        child: Material(
          color: Colors.transparent,
          child: Container(
            constraints: BoxConstraints(
              maxWidth: tooltipWidth,
              maxHeight: tooltipMaxHeight,
            ),
            child: entity_card.EntityProfileCard(
              entity: entity,
              width: tooltipWidth,
              leftMargin: 0, // Remove the default left margin for tooltips
            ),
          ),
        ),
      ),
    );

    // Add the overlay to the screen
    Overlay.of(context).insert(_profileTooltipOverlay!);
  }

  // Hide entity profile tooltip
  void _hideEntityProfileTooltip() {
    _profileTooltipOverlay?.remove();
    _profileTooltipOverlay = null;
    _lastClickTime = DateTime.now();
  }
}
