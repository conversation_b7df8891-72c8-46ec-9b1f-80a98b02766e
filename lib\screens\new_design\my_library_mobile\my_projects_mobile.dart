import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/hover_create_button.dart';
import 'package:nsl/screens/web/new_design/widgets/hover_nav_item.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';
import 'package:provider/provider.dart';
import 'package:carousel_slider/carousel_slider.dart';

class Project {
  final String projectName;
  final String createdOn;
  final String lastModified;
  final String lastModifiedBy;
  final String status;
  final DateTime lastUpdated;

  Project({
    required this.projectName,
    required this.createdOn,
    required this.lastModified,
    required this.lastModifiedBy,
    required this.status,
    required this.lastUpdated,
  });

  factory Project.fromJson(Map<String, dynamic> json) {
    return Project(
      projectName: json['projectName'] as String,
      createdOn: json['createdOn'] as String,
      lastModified: json['lastModified'] as String,
      lastModifiedBy: json['lastModifiedBy'] as String,
      status: json['status'] as String,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }
}

class MyProjectsScreenMobile extends StatefulWidget {
  const MyProjectsScreenMobile({
    super.key,
    this.showNavigationBar = true,
    this.searchQuery,
  });

  final bool showNavigationBar;
  final String? searchQuery;

  @override
  State<MyProjectsScreenMobile> createState() => MyProjectsScreenMobileState();
}

class MyProjectsScreenMobileState extends State<MyProjectsScreenMobile>
    with TickerProviderStateMixin {
  // Constants
  static const double _projectsPerViewNormal = 1.25; // Show 1 full card + 25% of next
  static const double _projectsPerViewCompact = 1.25;
  static const double _projectAspectRatio = 0.75; // Taller cards (height > width)
  static const double _verticalSpacing = 12.0;
  static const double _projectSpacing = 12.0; // Reduced spacing between cards
  static const double _horizontalPadding = 16.0;
  static const int _recentProjectsLimit = 10;

  // Text Styles
  static const TextStyle _sectionHeadingStyle = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: Colors.black,
    fontFamily: "TiemposText",
  );

  static const TextStyle _emptyStateStyle = TextStyle(
    fontSize: 16,
    color: Colors.grey,
    fontFamily: "TiemposText",
  );

  // Data
  List<Project> projects = [];
  List<Project> recentProjects = [];
  List<Project> allProjects = [];
  List<Project> filteredRecentProjects = [];
  List<Project> filteredAllProjects = [];
  bool isLoading = true;

  // Controllers
  late CarouselSliderController _recentProjectsController;
  late CarouselSliderController _allProjectsController;
  late AnimationController _loadingAnimationController;

  // Search state
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  // Animations
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // UI state
  bool _isKeyboardVisible = false;
  String _searchQuery = '';

  // Create project modal state
  bool _showCreateProjectModal = false;
  final TextEditingController _projectNameController = TextEditingController();
  String? _selectedIndustry;
  bool _isDropdownFocused = false;

  final List<String> _industries = [
    'E-Commerce',
    'Healthcare',
    'Finance',
    'Education',
    'Technology',
  ];

  // JSON string containing project data
  static const String projectsJsonString = '''
{
  "projects": [
    {
      "projectName": "E Commerce Solution",
      "createdOn": "09/05/2025",
      "lastModified": "10/06/2025",
      "lastModifiedBy": "Debmalaya Mitra",
      "status": "Discovery 50% / Development 0%",
      "lastUpdated": "2024-12-15T10:30:00Z"
    },
    {
      "projectName": "Fashion & Apparel Platform",
      "createdOn": "08/05/2025",
      "lastModified": "09/06/2025",
      "lastModifiedBy": "John Smith",
      "status": "Discovery 80% / Development 20%",
      "lastUpdated": "2024-12-18T14:22:00Z"
    },
    {
      "projectName": "Financial Advisory System",
      "createdOn": "07/05/2025",
      "lastModified": "08/06/2025",
      "lastModifiedBy": "Sarah Johnson",
      "status": "Discovery 100% / Development 45%",
      "lastUpdated": "2024-12-10T09:15:00Z"
    },
    {
      "projectName": "Home Rentals Platform",
      "createdOn": "06/05/2025",
      "lastModified": "07/06/2025",
      "lastModifiedBy": "Mike Wilson",
      "status": "Discovery 30% / Development 0%",
      "lastUpdated": "2024-12-20T16:45:00Z"
    },
    {
      "projectName": "Online Grocery Store",
      "createdOn": "05/05/2025",
      "lastModified": "06/06/2025",
      "lastModifiedBy": "Emily Davis",
      "status": "Discovery 90% / Development 60%",
      "lastUpdated": "2024-12-08T11:30:00Z"
    },
    {
      "projectName": "Courier & Logistics App",
      "createdOn": "04/05/2025",
      "lastModified": "05/06/2025",
      "lastModifiedBy": "David Brown",
      "status": "Discovery 70% / Development 30%",
      "lastUpdated": "2024-12-19T13:20:00Z"
    },
    {
      "projectName": "Automotive Marketplace",
      "createdOn": "03/05/2025",
      "lastModified": "04/06/2025",
      "lastModifiedBy": "Lisa Anderson",
      "status": "Discovery 60% / Development 15%",
      "lastUpdated": "2024-12-12T08:45:00Z"
    },
    {
      "projectName": "Fitness & Wellness App",
      "createdOn": "02/05/2025",
      "lastModified": "03/06/2025",
      "lastModifiedBy": "Robert Taylor",
      "status": "Discovery 85% / Development 40%",
      "lastUpdated": "2024-12-21T15:10:00Z"
    },
    {
      "projectName": "Real Estate Portal",
      "createdOn": "01/05/2025",
      "lastModified": "02/06/2025",
      "lastModifiedBy": "Jennifer White",
      "status": "Discovery 95% / Development 70%",
      "lastUpdated": "2024-12-07T12:00:00Z"
    },
    {
      "projectName": "Restaurant & Cafe",
      "createdOn": "31/04/2025",
      "lastModified": "01/06/2025",
      "lastModifiedBy": "Alex Chen",
      "status": "Discovery 40% / Development 10%",
      "lastUpdated": "2024-12-16T17:30:00Z"
    },
    {
      "projectName": "Healthcare Platform",
      "createdOn": "30/04/2025",
      "lastModified": "31/05/2025",
      "lastModifiedBy": "Maria Garcia",
      "status": "Discovery 75% / Development 35%",
      "lastUpdated": "2024-12-22T09:25:00Z"
    },
    {
      "projectName": "Education Portal",
      "createdOn": "29/04/2025",
      "lastModified": "30/05/2025",
      "lastModifiedBy": "James Wilson",
      "status": "Discovery 65% / Development 25%",
      "lastUpdated": "2024-12-05T14:40:00Z"
    }
  ]
}
''';

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeAnimations();
    _loadProjects();
  }

  @override
  void didUpdateWidget(MyProjectsScreenMobile oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.searchQuery != widget.searchQuery) {
      _searchQuery = widget.searchQuery ?? '';
      _filterProjects();
    }
  }

  void _initializeControllers() {
    _recentProjectsController = CarouselSliderController();
    _allProjectsController = CarouselSliderController();
    _searchQuery = widget.searchQuery ?? '';
    if (widget.showNavigationBar) {
      _searchController.addListener(_onSearchChanged);
      _searchFocusNode.addListener(_onSearchFocusChange);
    }
  }

  void _initializeAnimations() {
    _loadingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _loadingAnimationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _loadingAnimationController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _onSearchFocusChange() {
    setState(() {
      // Trigger rebuild to check keyboard visibility in build method
    });
  }

  void _onSearchChanged() {
    final query = _searchController.text.toLowerCase().trim();
    setState(() {
      _searchQuery = query;
      _filterProjects();
    });
  }

  void _filterProjects() {
    if (_searchQuery.isEmpty) {
      filteredRecentProjects = recentProjects;
      filteredAllProjects = allProjects;
    } else {
      filteredRecentProjects = recentProjects.where((project) {
        return project.projectName.toLowerCase().contains(_searchQuery) ||
            project.lastModifiedBy.toLowerCase().contains(_searchQuery) ||
            project.status.toLowerCase().contains(_searchQuery);
      }).toList();

      filteredAllProjects = allProjects.where((project) {
        return project.projectName.toLowerCase().contains(_searchQuery) ||
            project.lastModifiedBy.toLowerCase().contains(_searchQuery) ||
            project.status.toLowerCase().contains(_searchQuery);
      }).toList();
    }
  }

  void _loadProjects() {
    try {
      final data = json.decode(projectsJsonString);
      final loadedProjects = (data['projects'] as List<dynamic>)
          .map((projectJson) =>
              Project.fromJson(projectJson as Map<String, dynamic>))
          .toList();

      // Keep original order for allProjects (as received from API/JSON)
      final originalOrderProjects = List<Project>.from(loadedProjects);

      // Sort projects by lastUpdated date (most recent first) for recentProjects
      loadedProjects.sort((a, b) => b.lastUpdated.compareTo(a.lastUpdated));

      setState(() {
        projects = originalOrderProjects; // Original order
        recentProjects =
            loadedProjects.take(_recentProjectsLimit).toList(); // Recent sorted
        allProjects = originalOrderProjects; // Original order (as from API/JSON)
        filteredRecentProjects = recentProjects; // Initialize filtered lists
        filteredAllProjects = allProjects;
        isLoading = false;
      });

      _loadingAnimationController.forward();
    } catch (e) {
      setState(() {
        projects = <Project>[];
        recentProjects = <Project>[];
        allProjects = <Project>[];
        isLoading = false;
      });
      debugPrint('Error loading projects: $e');
    }
  }

  @override
  void dispose() {
    // CarouselSliderController doesn't have dispose method
    if (widget.showNavigationBar) {
      _searchController.removeListener(_onSearchChanged);
      _searchController.dispose();
      _searchFocusNode.removeListener(_onSearchFocusChange);
      _searchFocusNode.dispose();
    }
    _loadingAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _updateKeyboardVisibility();
    return _buildProjectsLibraryView();
  }

  void _updateKeyboardVisibility() {
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = keyboardHeight > 0;

    if (_isKeyboardVisible != isKeyboardVisible) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _isKeyboardVisible = isKeyboardVisible;
          });
        }
      });
    }
  }

  Widget _buildProjectsLibraryView() {
    return Scaffold(
      backgroundColor: widget.showNavigationBar
          ? const Color(0xfff6f6f6)
          : Colors.transparent,
      drawer: widget.showNavigationBar ? const CustomDrawer() : null,
      appBar: widget.showNavigationBar ? _buildAppBar() : null,
      body: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Only show header when showNavigationBar is true
              if (widget.showNavigationBar) ...[
                // Mobile-first header layout
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title and Create Button Row
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // Project title on the left
                          Text(
                            'Projects',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes .bodyLarge(context),
                              fontWeight: FontWeight.w600,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black87,
                            ),
                          ),
                          // Create Project button on the right
                          _MobileCreateButton(
                            onPressed: () {
                              setState(() {
                                _showCreateProjectModal = true;
                              });
                            },
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      // Full-width search bar below title
                      _buildFullWidthSearchBar(),
                    ],
                  ),
                ),
              ],
              // Main content
              _buildProjectsContent(),
            ],
          ),
          // Create Project Modal
          if (_showCreateProjectModal) _buildCreateProjectModal(),
        ],
      ),
    );
  }

  Widget _buildFullWidthSearchBar() {
    return Container(
      width: double.infinity,
      height: 44,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Search icon
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 12),
            child: Icon(
              Icons.search,
              color: Colors.grey,
              size: 20,
            ),
          ),
          // Search text field
          Expanded(
            child: TextField(
              controller: _searchController,
              focusNode: _searchFocusNode,
              decoration: InputDecoration(
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                hintText: 'Search projects...',
                border: InputBorder.none,
                hintStyle: TextStyle(
                  fontSize: ResponsiveFontSizes .bodyMedium(context),
                  color: Colors.grey.shade500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
                isDense: true,
                contentPadding: const EdgeInsets.symmetric(vertical: 12),
              ),
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes .bodyMedium(context),
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
          ),
          // Filter icon
          Padding(
            padding: const EdgeInsets.only(right: 8),
            child: IconButton(
              icon: const Icon(
                Icons.filter_list,
                color: Colors.grey,
                size: 20,
              ),
              onPressed: () {
                // Handle filter action
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProjectsContent() {
    return Expanded(
      child: isLoading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.fromLTRB(16, 8, 0, 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Recent Projects Section with Create Button
                  _buildSectionHeadingWithButton("Recent Projects"),
                  const SizedBox(height: 12),
                  Expanded(
                    flex: 1,
                    child: _buildRecentProjectsCarousel(),
                  ),
                  const SizedBox(height: 16),
                  // All Projects Section
                  _buildSectionHeading("All Projects"),
                  const SizedBox(height: 12),
                  Expanded(
                    flex: 1,
                    child: _buildAllProjectsCarousel(),
                  ),
                ],
              ),
            ),
    );
  }

  /// Builds section heading
  Widget _buildSectionHeading(String title) {
    return Text(title, style: _sectionHeadingStyle);
  }

  /// Builds section heading with create button on the right
  Widget _buildSectionHeadingWithButton(String title) {
    return Container(
      padding: EdgeInsets.only(right: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title, style: _sectionHeadingStyle),
          _MobileCreateButton(
            onPressed: () {
              setState(() {
                _showCreateProjectModal = true;
              });
            },
          ),
        ],
      ),
    );
  }

  /// Builds Recent Projects carousel
  Widget _buildRecentProjectsCarousel() {
    final projectsToShow =
        _searchQuery.isEmpty ? recentProjects : filteredRecentProjects;
    return _buildCarousel(
      projects: projectsToShow,
      controller: _recentProjectsController,
      emptyMessage: _searchQuery.isEmpty
          ? 'No recent projects found'
          : 'No recent projects match your search',
    );
  }

  /// Builds All Projects carousel
  Widget _buildAllProjectsCarousel() {
    final projectsToShow = _searchQuery.isEmpty ? allProjects : filteredAllProjects;
    return _buildCarousel(
      projects: projectsToShow,
      controller: _allProjectsController,
      emptyMessage: _searchQuery.isEmpty
          ? 'No projects found'
          : 'No projects match your search',
    );
  }

  /// Builds a generic carousel widget
  Widget _buildCarousel({
    required List<Project> projects,
    required CarouselSliderController controller,
    required String emptyMessage,
  }) {
    if (projects.isEmpty) {
      return Center(
        child: Text(emptyMessage, style: _emptyStateStyle),
      );
    }

    final projectDimensions = _calculateProjectDimensions(context);
    final projectWidth = projectDimensions['width']!;
    final projectHeight = projectDimensions['height']!;

    return CarouselSlider.builder(
      carouselController: controller,
      itemCount: projects.length,
      itemBuilder: (context, index, realIndex) {
        return _buildProjectItem(projects[index], index);
      },
      options: CarouselOptions(
        height: projectHeight,
        viewportFraction: 0.8, // Show 1 full card + 25% of next (80% viewport)
        enableInfiniteScroll: false,
        enlargeCenterPage: false,
        scrollDirection: Axis.horizontal,
        padEnds: false,
        disableCenter: true, // Align to left
      ),
    );
  }

  /// Calculate item extent for CarouselView
  double _calculateItemExtent() {
    final screenWidth = MediaQuery.of(context).size.width;
    final projectsPerView = _getProjectsPerView();
    final availableWidth = screenWidth - (_horizontalPadding * 2);
    return availableWidth / projectsPerView;
  }

  /// Builds individual project item widget
  Widget _buildProjectItem(Project project, int projectIndex) {
    return GestureDetector(
      onTap: () => _navigateToProjectDetails(projectIndex, project.projectName),
      child: AnimatedBuilder(
        animation: _loadingAnimationController,
        builder: (context, child) => FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: _buildProjectContent(project),
          ),
        ),
      ),
    );
  }

  /// Builds the project content (card layout)
  Widget _buildProjectContent(Project project) {
    final projectDimensions = _calculateProjectDimensions(context);

    return Container(
      width: projectDimensions['width']!,
      height: projectDimensions['height']!,
      margin: EdgeInsets.only(right: _projectSpacing),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(4), // Ultra minimal padding
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Project name and edit button
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    project.projectName,
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s14,
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black87,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                IconButton(
                  onPressed: () => _editProject(project),
                  icon: const Icon(
                    Icons.edit_outlined,
                    size: 16,
                    color: Colors.grey,
                  ),
                  constraints: const BoxConstraints(),
                  padding: EdgeInsets.zero,
                ),
              ],
            ),
            const SizedBox(height: 3), // Ultra minimal spacing
            // Project details
            _buildDetailRow('Created', project.createdOn),
            const SizedBox(height: 1), // Ultra minimal spacing
            _buildDetailRow('Modified', project.lastModified),
            const SizedBox(height: 1), // Ultra minimal spacing
            _buildDetailRow('By', project.lastModifiedBy),
            const SizedBox(height: 3), // Ultra minimal spacing
            // Status
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 3, vertical: 1), // Ultra compact padding
              decoration: BoxDecoration(
                color: Color(0xff0058FF).withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                project.status,
                style: FontManager.getCustomStyle(
                   fontSize: ResponsiveFontSizes .bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Color(0xff0058FF),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 90,
          child: Text(
            label,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes .bodyMedium(context),
              fontWeight: FontWeight.w500,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey.shade600,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes .bodyMedium(context),
              fontWeight: FontWeight.w500,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black87,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  /// Navigates to project details screen
  void _navigateToProjectDetails(int projectIndex, String projectName) {
    // Handle project navigation
    print('Tapped on project: $projectName');
  }

  /// Edit project action
  void _editProject(Project project) {
    // Handle edit action
    print('Edit pressed for project: ${project.projectName}');
  }

  /// Get projects per view based on keyboard visibility (for smooth scrolling)
  double _getProjectsPerView() {
    return _isKeyboardVisible ? _projectsPerViewCompact : _projectsPerViewNormal;
  }

  /// Calculate dynamic project dimensions based on available space
  Map<String, double> _calculateProjectDimensions(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final availableWidth = screenWidth - _horizontalPadding; // Only left padding, no right padding

    // Calculate width to show 1 full card + 25% of next card
    // With 80% viewport fraction, we need to calculate the actual card width
    double projectWidth = (availableWidth * 0.8) - _projectSpacing;

    // Size constraints for different modes
    if (_isKeyboardVisible) {
      projectWidth = projectWidth.clamp(250.0, 320.0);
    } else {
      projectWidth = projectWidth.clamp(280.0, 350.0);
    }

    // Calculate extremely compact auto height based on content
    // Title + spacing + 3 detail rows + spacing + status
    final double titleHeight = 14.0; // Extremely compact title height
    final double detailRowHeight = 10.0; // Each detail row (extremely compact)
    final double statusHeight = 14.0; // Status container height (extremely compact)
    final double totalSpacing = 3 + 3 + 1 + 1 + 3; // Ultra minimal spacing
    final double padding = 8.0; // Ultra minimal padding (4 * 2)
    
    final projectHeight = titleHeight + (detailRowHeight * 3) + statusHeight + totalSpacing + padding;

    return {
      'width': projectWidth,
      'height': projectHeight,
      'spacing': _projectSpacing,
    };
  }

  /// Calculate dynamic height for CarouselView based on project content
  double _calculateCarouselHeight() {
    final projectDimensions = _calculateProjectDimensions(context);
    final projectHeight = projectDimensions['height']!;
    return projectHeight + _verticalSpacing;
  }

  Widget _buildCreateProjectModal() {
    return GestureDetector(
      onTap: () {
        setState(() {
          _showCreateProjectModal = false;
        });
      },
      child: Container(
        color: Colors.black87.withOpacity(0.5),
        child: Center(
          child: GestureDetector(
            onTap: () {}, // Prevent closing when tapping on the modal content
            child: Container(
              width: MediaQuery.of(context).size.width * 0.9,
              constraints: const BoxConstraints(maxWidth: 400),
              margin: const EdgeInsets.symmetric(horizontal: 16),
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black87.withOpacity(0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title
                  Container(
                    alignment: Alignment.center,
                    child: Text(
                      'Create A Project',
                      style: FontManager.getCustomStyle(
                     fontSize: ResponsiveFontSizes .headlineMedium(context),
                        fontWeight: FontWeight.w600,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Color(0xff0058FF),
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Name field
                  Text(
                    'Name',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes .bodyMedium(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    height: 44,
                    child: TextField(
                      controller: _projectNameController,
                      decoration: InputDecoration(
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(color: Colors.grey.shade300),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide:
                              BorderSide(color: Color(0xff0058FF), width: 1),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 12),
                        hintText: 'Enter project name',
                        hintStyle: TextStyle(
                          color: Colors.grey.shade500,
                          fontSize: FontManager.s14,
                          fontFamily: FontManager.fontFamilyTiemposText,
                        ),
                      ),
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s14,
                        fontFamily: FontManager.fontFamilyTiemposText,
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),

                  // Industry field
                  Text(
                    'Industry',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes .bodyMedium(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    height: 44,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: _isDropdownFocused
                            ? Color(0xff0058FF)
                            : Colors.grey.shade300,
                        width: 1,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        value: _selectedIndustry,
                        hint: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 12),
                          child: Text(
                            'Select industry',
                            style: FontManager.getCustomStyle(
                             fontSize: ResponsiveFontSizes .bodyMedium(context),
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.grey.shade500,
                            ),
                          ),
                        ),
                        icon: Padding(
                          padding: const EdgeInsets.only(right: 12),
                          child: Icon(
                            Icons.keyboard_arrow_down,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        isExpanded: true,
                        items: _industries.map((String industry) {
                          return DropdownMenuItem<String>(
                            value: industry,
                            child: Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 12),
                              child: Text(
                                industry,
                                style: FontManager.getCustomStyle(
                                  fontSize: ResponsiveFontSizes .bodyMedium(context),
                                  fontFamily:
                                      FontManager.fontFamilyTiemposText,
                                ),
                              ),
                            ),
                          );
                        }).toList(),
                        onChanged: (String? newValue) {
                          setState(() {
                            _selectedIndustry = newValue;
                          });
                        },
                      ),
                    ),
                  ),
                  const SizedBox(height: 32),

                  // Start button
                  Center(
                    child: SizedBox(
                      width: double.infinity,
                      height: 44,
                      child: ElevatedButton(
                        onPressed: () {
                          // Handle project creation
                          if (_projectNameController.text.isNotEmpty &&
                              _selectedIndustry != null) {
                            // Create new project object
                            final newProject = Project(
                              projectName: _projectNameController.text,
                              createdOn: DateTime.now()
                                  .toString()
                                  .substring(0, 10)
                                  .replaceAll('-', '/'),
                              lastModified: DateTime.now()
                                  .toString()
                                  .substring(0, 10)
                                  .replaceAll('-', '/'),
                              lastModifiedBy: 'Current User',
                              status: 'Discovery 0% / Development 0%',
                              lastUpdated: DateTime.now(),
                            );

                            // Store project name for success message
                            final projectName = _projectNameController.text;

                            // Add new project to the beginning of the list
                            setState(() {
                              projects.insert(0, newProject);
                              recentProjects.insert(0, newProject);
                              allProjects.insert(0, newProject);
                              filteredRecentProjects.insert(0, newProject);
                              filteredAllProjects.insert(0, newProject);

                              // Close modal and reset form
                              _showCreateProjectModal = false;
                              _projectNameController.clear();
                              _selectedIndustry = null;
                            });

                            // Show success message
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                    'Project "$projectName" created successfully!'),
                                backgroundColor: Colors.green,
                              ),
                            );
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Color(0xff0058FF),
                          foregroundColor: Colors.white,
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          'Start',
                          style: FontManager.getCustomStyle(
                           fontSize: ResponsiveFontSizes .bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// Mobile-optimized Create Button
class _MobileCreateButton extends StatelessWidget {
  final VoidCallback onPressed;

  const _MobileCreateButton({required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 36,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Color(0xff0058FF),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Color(0xff0058FF).withOpacity(0.2),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(8),
          child: Center(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.add,
                  color: Colors.white,
                  size: 18,
                ),
                const SizedBox(width: 4),
                Text(
                  'Create',
                  style: FontManager.getCustomStyle(
                   fontSize: ResponsiveFontSizes .bodyMedium(context),
                    fontWeight: FontWeight.w600,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}



  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: const Color(0xfff6f6f6),
      surfaceTintColor: Colors.transparent,
      foregroundColor: Colors.black,
      elevation: 0,
      automaticallyImplyLeading: false,
      titleSpacing: 0,
      title: Row(
        children: [
          Builder(
            builder: (context) => IconButton(
              icon: const Icon(Icons.menu, color: Colors.black, size: 24),
              onPressed: () => Scaffold.of(context).openDrawer(),
              padding: const EdgeInsets.symmetric(horizontal: 16),
            ),
          ),
          const SizedBox(width: 56),
        ],
      ),
    );
  }
