import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/providers/auth_provider.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/screens/new_design/my_business/collection.dart';
import 'package:nsl/screens/new_design/my_business/home.dart';
import 'package:nsl/screens/new_design/my_library_mobile/create_book_mobile.dart';

import 'package:nsl/screens/new_design/my_business/solution.dart';
import 'package:nsl/screens/new_design/my_library_mobile/my_projects_mobile.dart';
import 'package:nsl/screens/new_design/tree_model_hierarchy_mobile/tree_model_hierarchy_mobile.dart';
import 'package:nsl/screens/web/new_design/tree_hierarchy_model.dart';
import 'package:nsl/screens/web_transaction/web_transaction_widgets_demo.dart';
import 'package:provider/provider.dart';

import 'custom_drawer_item.dart';

class CustomDrawer extends StatelessWidget {
  const CustomDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      child: Drawer(
        backgroundColor: Color(0xffF6F6F6),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.zero),
        child: Column(
          children: [
            // const SizedBox(height: 20),
            const SizedBox(height: 40),
            // Profile Header
            Container(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.zero,
                // border: Border(
                //   bottom: BorderSide(color: Colors.grey.shade200, width: 2),
                // ),
              ),
              child: Row(
                children: [
                  SvgPicture.asset("assets/images/profile.svg"),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Consumer<AuthProvider>(
                      builder: (context, authProvider, _) {
                        final String profileName =
                            authProvider.user?.username ?? 'Nsl Admin';
                        return Text(
                          profileName,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                          ),
                        );
                      },
                    ),
                  ),
                  Container(
                    width: 30,
                    height: 30,
                    decoration: BoxDecoration(
                      color: Color(0xffE5EBFD),
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      padding: EdgeInsets.zero, // Remove default padding
                      icon: Icon(Icons.close, size: 16, color: Colors.black),
                      onPressed: () => Navigator.pop(context),
                      constraints:
                          BoxConstraints(), // Remove default constraints
                    ),
                  ),
                ],
              ),
            ),

            // Drawer Items
            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                children: [
                  CustomDrawerItem(
                    icon: 'assets/images/sidebar_icons/chat.svg',
                    title: 'Chat',
                    onTap: () {
                      final provider =
                          Provider.of<WebHomeProvider>(context, listen: false);
                      provider.clearMessages();
                      provider.selectedQuickMessage = 'NSL';
                      Navigator.pop(context);
                    },
                  ),
                  CustomDrawerItem(
                    icon: 'assets/images/sidebar_icons/create.svg',
                    title: 'Create',
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const CreateBookMobile(),
                        ),
                      );
                    },
                  ),
                  CustomDrawerItem(
                    icon: 'assets/images/sidebar_icons/business.svg',
                    title: 'My Business',
                    onTap: null,
                    children: [
                      ListTile(
                        leading: SvgPicture.asset(
                          'assets/images/my_business/home_business.svg',
                          width: 20,
                          height: 20,
                        ),
                        title: const Text('Home'),
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => MyBusinessHome()),
                          );
                        },
                      ),
                      ListTile(
                          leading: SvgPicture.asset(
                            'assets/images/my_business/collection_business.svg',
                            width: 20,
                            height: 20,
                          ),
                          title: const Text('Collections'),
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => MyBusinessCollection(
                                        screenType: "collections",
                                      )),
                            );
                          }),
                      ListTile(
                          leading: SvgPicture.asset(
                            'assets/images/my_business/solutions_business.svg',
                            width: 20,
                            height: 20,
                          ),
                          title: const Text('Solutions'),
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => MyBusinessSolution()),
                            );
                          }),
                      ListTile(
                        leading: SvgPicture.asset(
                          'assets/images/my_business/records_business.svg',
                          width: 20,
                          height: 20,
                        ),
                        title: const Text('Records'),
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => MyBusinessCollection(
                                      screenType: "records",
                                    )),
                          );
                        },
                      ),
                    ],
                  ),
                  CustomDrawerItem(
                    icon: 'assets/images/sidebar_icons/my_transaction.svg',
                    title: 'MY Transaction',
                    onTap: () => Navigator.pop(context),
                  ),
                  CustomDrawerItem(
                    icon: 'assets/images/sidebar_icons/calendar.svg',
                    title: 'Calendar',
                    onTap: () => Navigator.pop(context),
                  ),
                  CustomDrawerItem(
                    icon: 'assets/images/sidebar_icons/notifications.svg',
                    title: 'Notification',
                    onTap: () => Navigator.pop(context),
                  ),
                  CustomDrawerItem(
                    icon: 'assets/images/sidebar_icons/code.svg',
                    title: 'Reconciliation',
                    onTap: () => Navigator.pop(context),
                  ),
                  CustomDrawerItem(
                    icon: 'assets/images/sidebar_icons/code.svg',
                    title: 'Tree Model',
                    onTap: () => {
                     Navigator.pop(context),
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const TreeHierarchyModelMobile(),
                        ),
                      )
                  }
                  ),
                   CustomDrawerItem(
                    icon: 'assets/images/sidebar_icons/code.svg',
                    title: 'Demo',
                    onTap: () => {
                     Navigator.pop(context),
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const WebTransactionWidgetsDemo(),
                        ),
                      )
                }
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
