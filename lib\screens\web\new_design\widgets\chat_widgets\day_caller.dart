import 'package:flutter/material.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';

class DayCaller extends StatelessWidget {
  final String? customMessage;
  final bool showSpecialThoughts;

  const DayCaller({
    Key? key,
    this.customMessage,
    this.showSpecialThoughts = false,
  }) : super(key: key);

  // Global special days data extracted from your Excel file
  static final Map<String, Map<String, String>> _specialDays = {
    // January Special Days Data
    '01-01': {
      'event': 'World Introvert Day',
      'thought':
          'To the quiet thinkers and calm creators — today we celebrate the strength in solitude.'
    },
    '01-02': {
      'event': 'Festival of Sleep Day',
      'thought':
          'Rest is not a luxury — it\'s a gift. Take a moment today to slow down and recharge.'
    },
    '01-03': {
      'event': 'World Braille Day',
      'thought':
          'Accessibility is dignity. Let\'s honor the power of communication beyond what we see.'
    },
    '01-04': {
      'event': 'World Day for War Orphans',
      'thought':
          'A reminder to open our hearts and create a world where every child feels safe and loved.'
    },
    '01-05': {
      'event': 'World Day for War Orphans',
      'thought':
          'May our actions today make space for healing and hope for the unseen and unheard.'
    },
    '01-06': {
      'event': 'International Programmers Day',
      'thought':
          'To the architects of our digital world — your code shapes the future. Thank you!'
    },
    '01-07': {
      'event': 'World Literacy Day',
      'thought':
          'Reading opens doors, writing opens minds — let\'s celebrate the freedom that literacy brings.'
    },
    '01-08': {
      'event': 'International Thank You Day',
      'thought':
          'Gratitude turns ordinary moments into blessings — today, don\'t forget to say \'thank you\'.'
    },
    '01-09': {
      'event': 'World Hindi Day',
      'thought':
          'A language that connects millions — let\'s honor the beauty and rhythm of Hindi today.'
    },
    '01-10': {
      'event': 'International Thank You Day',
      'thought':
          'Kind words cost nothing, but their value is endless. Say thank you with heart today.'
    },
    '01-11': {
      'event': 'World Youth Day',
      'thought':
          'The energy of youth is the promise of tomorrow — let\'s nurture it with trust and opportunity.'
    },
    '01-12': {
      'event': 'World Skeptics Day',
      'thought':
          'Questioning is thinking — curiosity is courage. Here\'s to the skeptics who help truth rise.'
    },
    '01-13': {
      'event': 'World Logic Day',
      'thought':
          'Logic is the compass in a chaotic world — may we use it wisely to navigate with clarity.'
    },
    '01-14': {
      'event': 'World Religion Day',
      'thought':
          'All paths lead to understanding when walked with respect — today, let\'s celebrate spiritual harmony.'
    },
    '01-15': {
      'event': 'World Economic Forum Day',
      'thought':
          'Great ideas move economies — may today spark visions that uplift communities worldwide.'
    },
    '01-16': {
      'event': 'World Day of Muslim Culture Peace Dialogue and Film',
      'thought':
          'Peace grows where culture is shared and stories are heard — let\'s honor the voices that unite.'
    },
    '01-17': {
      'event': 'World Thesaurus Day',
      'thought':
          'Words have power — and today, we celebrate every synonym that helps us express better.'
    },
    '01-18': {
      'event': 'World Quark Day',
      'thought':
          'Even the tiniest particles shape the universe — a reminder that every small effort matters.'
    },
    '01-19': {
      'event': 'World Day of Religion',
      'thought':
          'Let\'s embrace the rich mosaic of beliefs that inspire kindness, compassion, and hope.'
    },
    '01-20': {
      'event': 'World Hug Day',
      'thought':
          'Sometimes a hug says what words cannot — reach out and wrap someone in warmth today.'
    },
    '01-21': {
      'event': 'World Day of Grandparents and the Elderly',
      'thought':
          'Our roots run deep — today we honor the wisdom and love of those who came before us.'
    },
    '01-22': {
      'event': 'World Handwriting Day',
      'thought':
          'In a digital world, a handwritten note still carries unmatched warmth. Try writing something today.'
    },
    '01-23': {
      'event': 'International Day of Education',
      'thought':
          'Education is the spark that lights the way forward — may we keep that flame alive for all.'
    },
    '01-24': {
      'event': 'World Leprosy Day',
      'thought':
          'Awareness breaks stigma. Let\'s stand for compassion and dignity for all affected by leprosy.'
    },
    '01-25': {
      'event': 'International Customs Day',
      'thought':
          'Borders may divide, but customs connect — here\'s to the unsung guardians of global harmony.'
    },
    '01-26': {
      'event': 'International Holocaust Remembrance Day',
      'thought':
          'May we remember, reflect, and rise against hatred in every form — never again.'
    },
    '01-27': {
      'event': 'Data Privacy Day',
      'thought':
          'Your data is your story. Let\'s stay informed and empowered in our digital lives.'
    },
    '01-28': {
      'event': 'International Puzzle Day',
      'thought':
          'Every challenge is a puzzle waiting to be solved — think, play, and enjoy the pieces today.'
    },
    '01-29': {
      'event': 'World Leprosy Day',
      'thought':
          'Let\'s spread knowledge, not stigma. Together, we build a more inclusive future.'
    },
    '01-30': {
      'event': 'World Leprosy Day',
      'thought':
          'Empathy is the beginning of healing — take a moment to understand and care today.'
    },
    '01-31': {
      'event': 'World Hijab Day & World Day of Social Justice',
      'thought':
          'Let every choice of expression be met with respect — understanding is the heart of unity. Justice is not a single day — it\'s a daily promise to do better.'
    },
// February Special Days Data
    '02-01': {
      'event': 'World Wetlands Day',
      'thought':
          'Wetlands breathe life into ecosystems — let\'s protect the lungs of our planet.'
    },
    '02-02': {
      'event': 'World Day Against the Use of Child Soldiers',
      'thought':
          'Every child deserves a childhood — not a battleground. Let\'s speak for those without a voice.'
    },
    '02-03': {
      'event': 'World Cancer Day',
      'thought':
          'Courage shines brightest in the face of adversity — here\'s to warriors, survivors, and hope.'
    },
    '02-04': {
      'event': 'World Nutella Day',
      'thought':
          'Some joys are simple and sweet — today, indulge a little in life\'s chocolatey happiness.'
    },
    '02-05': {
      'event':
          'International Day of Zero Tolerance to Female Genital Mutilation',
      'thought':
          'Zero tolerance means every voice counts — protect, empower, and never stay silent.'
    },
    '02-06': {
      'event': 'World Day of the Sick',
      'thought':
          'May care, compassion, and comfort reach all those silently enduring illness.'
    },
    '02-07': {
      'event': 'World Epilepsy Day',
      'thought':
          'Epilepsy is just one part of a person — let\'s replace stigma with understanding.'
    },
    '02-08': {
      'event': 'World Pizza Day',
      'thought':
          'A slice of joy, a taste of fun — may today be deliciously lighthearted!'
    },
    '02-09': {
      'event': 'World Pulses Day',
      'thought':
          'Tiny seeds, mighty nourishment — pulses feed the world sustainably.'
    },
    '02-10': {
      'event': 'International Day of Women and Girls in Science',
      'thought':
          'Empowered girls in science build a stronger, smarter tomorrow. Let\'s cheer them on!'
    },
    '02-11': {
      'event': 'World Day Against the Use of Child Soldiers',
      'thought':
          'A world that protects children is a world worth building — let\'s fight exploitation together.'
    },
    '02-12': {
      'event': 'World Radio Day',
      'thought':
          'Voices on the airwaves shape minds — let\'s value free, inclusive communication.'
    },
    '02-13': {
      'event': 'World Day of Love',
      'thought':
          'Love, in every form, is what makes us human — may today be filled with it.'
    },
    '02-14': {
      'event': 'International Childhood Cancer Day',
      'thought':
          'Let\'s stand with brave little hearts and their families — your strength inspires the world.'
    },
    '02-15': {
      'event': 'World Day of Social Justice',
      'thought':
          'Justice means fairness for all — may our systems always strive to reflect this truth.'
    },
    '02-16': {
      'event': 'World Day of Human Justice',
      'thought':
          'Human dignity deserves human justice — let\'s listen, act, and advocate.'
    },
    '02-17': {
      'event': 'World Day of Social Justice',
      'thought':
          'Justice is not just a concept — it\'s a commitment we make to each other, daily.'
    },
    '02-18': {
      'event': 'World Day of Marine Mammal Protection',
      'thought':
          'Every creature matters. Let\'s honor and protect our marine kin today.'
    },
    '02-19': {
      'event': 'World Day of Social Justice',
      'thought':
          'We rise when we stand up for what\'s right — today, and every day.'
    },
    '02-20': {
      'event': 'International Mother Language Day',
      'thought':
          'Language is culture, memory, and connection — speak your truth, in your own words.'
    },
    '02-21': {
      'event': 'World Day of Encephalitis',
      'thought':
          'Encephalitis may be rare, but awareness is powerful. Knowledge saves lives.'
    },
    '02-22': {
      'event': 'World Day of Peace and Understanding',
      'thought':
          'Peace and understanding begin in conversation — let\'s listen more, and judge less.'
    },
    '02-23': {
      'event': 'World NGO Day',
      'thought':
          'From grassroots to global, NGOs build change — let\'s celebrate their tireless work.'
    },
    '02-24': {
      'event': 'World Day of Social Justice',
      'thought':
          'Real justice uplifts. Let\'s use today to amplify silenced voices and right long-standing wrongs.'
    },
    '02-25': {
      'event': 'World Day for the Fight Against Sexual Exploitation',
      'thought':
          'Every person has the right to be safe from exploitation — let\'s stand as shields for the vulnerable.'
    },
    '02-26': {
      'event': 'World NGO Day',
      'thought':
          'Nonprofits are the heartbeat of humanity — support their mission, share their light.'
    },
    '02-27': {
      'event': 'World Day of Social Justice',
      'thought':
          'Change happens when justice becomes action — be that spark today.'
    },
    '02-28': {
      'event': 'Zero Discrimination Day',
      'thought':
          'Different does not mean less. Let\'s choose empathy over judgment.'
    },
// March Special Days Data
    '03-01': {
      'event': 'World Day of Prayer',
      'thought':
          'In quiet reflection or shared hope — prayer connects us beyond differences.'
    },
    '03-02': {
      'event': 'World Wildlife Day',
      'thought':
          'Wildlife belongs in the wild — may we protect nature\'s irreplaceable wonders.'
    },
    '03-03': {
      'event': 'World Day of Fight Against Sexual Exploitation',
      'thought':
          'Freedom begins with safety — say no to abuse, say yes to dignity.'
    },
    '03-04': {
      'event': 'World Day of Prayer',
      'thought':
          'Faith can unite — may our prayers build bridges and comfort hearts today.'
    },
    '03-05': {
      'event': 'World Glaucoma Day',
      'thought':
          'Your sight is a gift — protect it with care, cherish it with gratitude.'
    },
    '03-06': {
      'event': 'World Day of Prayer',
      'thought':
          'A moment of prayer can bring calm in chaos — may your spirit feel peace today.'
    },
    '03-07': {
      'event': 'International Women\'s Day',
      'thought':
          'To every woman — your voice, dreams, and strength shape the world. Today, we celebrate you.'
    },
    '03-08': {
      'event': 'World DJ Day',
      'thought':
          'Spinning beats and lifting moods — to the DJs who soundtrack our joy, thank you!'
    },
    '03-09': {
      'event': 'World Kidney Day',
      'thought':
          'Healthy kidneys, healthy life — stay hydrated, stay aware, stay grateful.'
    },
    '03-10': {
      'event': 'World Plumbing Day',
      'thought':
          'Behind every sink and faucet is the brilliance of plumbing — let\'s appreciate the flow.'
    },
    '03-11': {
      'event': 'World Day Against Cyber Censorship',
      'thought':
          'Freedom online is a right — let\'s protect it for all, not just the privileged.'
    },
    '03-12': {
      'event': 'World Sleep Day',
      'thought':
          'Rest is renewal. Your well-being matters — take time to breathe deeply today.'
    },
    '03-13': {
      'event': 'International Day of Mathematics',
      'thought':
          'Math is everywhere — in music, design, nature. Let\'s marvel at its magic!'
    },
    '03-14': {
      'event': 'World Consumer Rights Day',
      'thought':
          'Your rights are your power — stay informed, stay vocal, stay fearless.'
    },
    '03-15': {
      'event': 'World Sleep Day',
      'thought':
          'Sleep is the reset we all need — honor your body\'s call to rest.'
    },
    '03-16': {
      'event': 'World Social Work Day',
      'thought':
          'Social workers hold communities together — thank you for your heart-led service.'
    },
    '03-17': {
      'event': 'World Sleep Day',
      'thought':
          'More rest equals better health — may we build cultures that value well-being.'
    },
    '03-18': {
      'event': 'World Oral Health Day',
      'thought':
          'A healthy smile can brighten the world — take care of it with joy.'
    },
    '03-19': {
      'event': 'International Day of Happiness',
      'thought':
          'Happiness isn\'t far — it lives in moments of connection, purpose, and kindness.'
    },
    '03-20': {
      'event': 'International Day for the Elimination of Racial Discrimination',
      'thought':
          'Equality isn\'t optional — today we rise against discrimination in every form.'
    },
    '03-21': {
      'event': 'World Water Day',
      'thought':
          'Every drop counts — let\'s value, protect, and preserve the water we share.'
    },
    '03-22': {
      'event': 'World Meteorological Day',
      'thought':
          'Weather connects us all — let\'s understand its language and respect its power.'
    },
    '03-23': {
      'event': 'World Tuberculosis Day',
      'thought':
          'Together, we fight. Together, we heal. Let\'s break the silence around TB.'
    },
    '03-24': {
      'event':
          'International Day of Remembrance of the Victims of Slavery and the Transatlantic Slave Trade',
      'thought':
          'Let us remember their suffering, honor their resilience, and promise: never again.'
    },
    '03-25': {
      'event': 'World Theatre Day',
      'thought':
          'The stage reflects life — raw, real, and revealing. Celebrate the power of performance.'
    },
    '03-26': {
      'event': 'World Theatre Day',
      'thought':
          'To act is to feel, to imagine, to inspire — theatre makes us more human.'
    },
    '03-27': {
      'event': 'Earth Hour',
      'thought':
          'One hour can make a statement — switch off, and show you care for Earth.'
    },
    '03-28': {
      'event': 'World Piano Day',
      'thought':
          'Strike a chord today — in music, in emotion, in purpose. Let it echo.'
    },
    '03-29': {
      'event': 'World Bipolar Day',
      'thought':
          'Mental health matters — may we see with compassion, feel with honesty, and support without judgment.'
    },
    '03-30': {
      'event': 'World Backup Day',
      'thought':
          'Back it up — your memories, your work, your peace of mind. Stay safe digitally.'
    },
    '03-31': {
      'event': 'World Autism Awareness Day',
      'thought':
          'Every mind is unique — let\'s build a world that celebrates, supports, and empowers all kinds of minds.'
    },
// April Special Days Data
    '04-01': {
      'event': 'World Autism Awareness Day',
      'thought':
          'Awareness fosters acceptance — may we choose inclusion every single day.'
    },
    '04-02': {
      'event': 'World Party Day',
      'thought':
          'Life\'s too short not to dance, laugh, and celebrate — let joy be contagious today!'
    },
    '04-03': {
      'event':
          'International Day for Mine Awareness and Assistance in Mine Action',
      'thought':
          'Hope clears even the most dangerous paths — let\'s help make every step safe again.'
    },
    '04-04': {
      'event': 'International Day of Conscience',
      'thought':
          'Your conscience is your compass — may it always point toward kindness and courage.'
    },
    '04-05': {
      'event': 'International Day of Sport for Development and Peace',
      'thought':
          'Sport unites, uplifts, and inspires — let\'s play for peace, not just victory.'
    },
    '04-06': {
      'event': 'World Health Day',
      'thought':
          'Health is true wealth — may we value it in ourselves and others.'
    },
    '04-07': {
      'event': 'International Romani Day',
      'thought':
          'Every culture is a color on humanity\'s canvas — let\'s celebrate diversity with pride.'
    },
    '04-08': {
      'event': 'World Gin Day',
      'thought':
          'To good spirits and even better company — cheers to slowing down and savoring life.'
    },
    '04-09': {
      'event': 'World Homeopathy Day',
      'thought':
          'Healing comes in many forms — may we honor every path that brings relief and balance.'
    },
    '04-10': {
      'event': 'World Parkinson\'s Day',
      'thought':
          'Strength isn\'t always visible — let\'s support those facing Parkinson\'s with empathy and hope.'
    },
    '04-11': {
      'event': 'International Day of Human Space Flight',
      'thought':
          'From curiosity to cosmos — the journey beyond Earth begins with the courage to look up.'
    },
    '04-12': {
      'event': 'International Plant Appreciation Day',
      'thought':
          'Nature\'s beauty is in the little things — pause and appreciate the quiet miracle of plants.'
    },
    '04-13': {
      'event': 'World Chagas Disease Day',
      'thought':
          'Awareness leads to action — together, we can defeat what once seemed undefeatable.'
    },
    '04-14': {
      'event': 'World Art Day',
      'thought':
          'Art speaks when words fail — let\'s make space for creativity in every part of life.'
    },
    '04-15': {
      'event': 'World Voice Day',
      'thought':
          'Your voice matters — use it to uplift, question, inspire, and heal.'
    },
    '04-16': {
      'event': 'World Hemophilia Day',
      'thought':
          'Support brings strength — let\'s champion awareness and compassion for rare conditions.'
    },
    '04-17': {
      'event': 'World Heritage Day',
      'thought':
          'Our heritage is our shared story — protect it, pass it on, and learn from it.'
    },
    '04-18': {
      'event': 'World Liver Day',
      'thought':
          'Your liver works hard — today, give it a little gratitude and a lot of care.'
    },
    '04-19': {
      'event': 'World Cannabis Day',
      'thought':
          'Beyond the buzz — let\'s talk facts, research, and responsible choices.'
    },
    '04-20': {
      'event': 'World Creativity and Innovation Day',
      'thought':
          'Innovation begins with imagination — may your ideas today become solutions tomorrow.'
    },
    '04-21': {
      'event': 'International Mother Earth Day',
      'thought':
          'This planet is our only home — let\'s protect it like something we can\'t replace.'
    },
    '04-22': {
      'event': 'World Book and Copyright Day',
      'thought':
          'Books carry the voices of generations — may your shelves always be full and your mind open.'
    },
    '04-23': {
      'event': 'World Day for Laboratory Animals',
      'thought':
          'Even the smallest creatures deserve our compassion — advocate for ethical science.'
    },
    '04-24': {
      'event': 'World Malaria Day',
      'thought':
          'We\'ve beaten malaria before — we can do it again. Awareness is the first step.'
    },
    '04-25': {
      'event': 'World Intellectual Property Day',
      'thought':
          'Creativity deserves protection — let\'s honor the minds behind every meaningful creation.'
    },
    '04-26': {
      'event': 'World Design Day',
      'thought':
          'Design shapes how we live — let\'s make it beautiful, accessible, and thoughtful.'
    },
    '04-27': {
      'event': 'World Day for Safety and Health at Work',
      'thought':
          'Every worker deserves safety — may awareness lead to stronger protections and dignity.'
    },
    '04-28': {
      'event': 'International Dance Day',
      'thought':
          'Dance is freedom in motion — move your body, lift your soul, celebrate your rhythm.'
    },
    '04-29': {
      'event': 'International Jazz Day',
      'thought':
          'Jazz is the sound of resilience — may your day be full of improvisation and joy.'
    },
    '04-30': {
      'event': 'International Workers\' Day',
      'thought':
          'Work is more than a task — it\'s dignity, purpose, and progress. Let\'s value every role.'
    },
// May Special Days Data
    '05-01': {
      'event': 'World Tuna Day',
      'thought':
          'A fish so ordinary, yet so vital — let\'s protect tuna and all that keeps oceans alive.'
    },
    '05-02': {
      'event': 'World Press Freedom Day',
      'thought':
          'Truth fuels democracy — today, we stand for the freedom to tell it boldly.'
    },
    '05-03': {
      'event': 'World Password Day',
      'thought':
          'A strong password protects your digital world — take a minute to level up your security.'
    },
    '05-04': {
      'event': 'World Hand Hygiene Day',
      'thought':
          'Clean hands save lives — let\'s keep the habit and help stop the spread.'
    },
    '05-05': {
      'event': 'World Asthma Day',
      'thought':
          'Every breath counts — may we breathe deeply, live fully, and support lung health for all.'
    },
    '05-06': {
      'event': 'World Athletics Day',
      'thought':
          'Play fair. Compete with heart. Let\'s celebrate every athlete\'s journey.'
    },
    '05-07': {
      'event': 'World Red Cross and Red Crescent Day',
      'thought':
          'When crisis strikes, they respond — let\'s honor the red and white of humanity.'
    },
    '05-08': {
      'event': 'Europe Day',
      'thought':
          'Together in diversity, united in purpose — may peace always be the center of Europe\'s heart.'
    },
    '05-09': {
      'event': 'World Lupus Day',
      'thought':
          'Strength lies in stories — let\'s stand with everyone living with lupus today.'
    },
    '05-10': {
      'event': 'World Migratory Bird Day',
      'thought':
          'Wings that cross continents remind us — protection knows no borders.'
    },
    '05-11': {
      'event': 'International Nurses Day',
      'thought':
          'To nurses — your care is a quiet force that heals the world. Thank you.'
    },
    '05-12': {
      'event': 'World Fair Trade Day',
      'thought':
          'Fair trade builds better lives — let your choices reflect your values.'
    },
    '05-13': {
      'event': 'World Migratory Bird Day',
      'thought':
          'Let the skies remind us — every journey needs safe passage and a shared responsibility.'
    },
    '05-14': {
      'event': 'International Day of Families',
      'thought':
          'Family is not just biology — it\'s where care and connection begin.'
    },
    '05-15': {
      'event': 'International Day of Light',
      'thought':
          'Light reveals, heals, and inspires — may today spark your brightest ideas.'
    },
    '05-16': {
      'event': 'World Telecommunication and Information Society Day',
      'thought':
          'From rural villages to outer space, connectivity creates possibility — let\'s bridge every gap.'
    },
    '05-17': {
      'event': 'International Museum Day',
      'thought':
          'Museums preserve memory — explore one today, and walk through time.'
    },
    '05-18': {
      'event': 'World IBD Day',
      'thought':
          'Invisible battles deserve visible support — awareness is the first step to understanding.'
    },
    '05-19': {
      'event': 'World Bee Day',
      'thought':
          'Bees pollinate life — let\'s protect them, or lose far more than honey.'
    },
    '05-20': {
      'event': 'World Day for Cultural Diversity for Dialogue and Development',
      'thought':
          'Diversity is strength — when we understand each other, peace follows naturally.'
    },
    '05-21': {
      'event': 'International Day for Biological Diversity',
      'thought':
          'Every species matters — biodiversity is our planet\'s most precious safety net.'
    },
    '05-22': {
      'event': 'International Day to End Obstetric Fistula',
      'thought':
          'Ending fistula means restoring dignity — let\'s amplify awareness and care.'
    },
    '05-23': {
      'event': 'World Schizophrenia Day',
      'thought':
          'Mental illness needs compassion, not silence — together, we can fight stigma.'
    },
    '05-24': {
      'event': 'World Thyroid Day',
      'thought':
          'A little gland, a big impact — let\'s raise awareness for thyroid health.'
    },
    '05-25': {
      'event': 'World Dracula Day',
      'thought':
          'Fictional or not, Dracula reminds us: some fears fade in the light of knowledge.'
    },
    '05-26': {
      'event': 'World MS Day',
      'thought':
          'MS is unpredictable — let\'s stand steady for those who face it daily.'
    },
    '05-27': {
      'event': 'World Hunger Day',
      'thought': 'No one should go hungry — today, take action to feed hope.'
    },
    '05-28': {
      'event': 'International Day of UN Peacekeepers',
      'thought':
          'Peacekeepers wear blue, but their impact is golden — thank you for standing between war and peace.'
    },
    '05-29': {
      'event': 'World MS Day',
      'thought':
          'Strength is continuing despite uncertainty — let\'s walk beside those with MS.'
    },
    '05-30': {
      'event': 'World No-Tobacco Day',
      'thought':
          'Choose health, choose life — let\'s build a world free from tobacco.'
    },
    '05-31': {
      'event': 'Global Day of Parents',
      'thought':
          'Parenting is the hardest job you\'ll never get paid for — celebrate the love behind the scenes.'
    },
// June Special Days Data
    '06-01': {
      'event': 'World Eating Disorders Action Day',
      'thought':
          'Behind every disorder is a person — let\'s lead with empathy, not judgment.'
    },
    '06-02': {
      'event': 'World Bicycle Day',
      'thought':
          'Two wheels, endless freedom — celebrate the power of the bicycle today.'
    },
    '06-03': {
      'event': 'International Day of Innocent Children Victims of Aggression',
      'thought':
          'No child should suffer because of conflict — may we protect innocence with all our might.'
    },
    '06-04': {
      'event': 'World Environment Day',
      'thought':
          'Earth is not a resource — it\'s a responsibility. Let\'s cherish and heal it together.'
    },
    '06-05': {
      'event': 'Russian Language Day',
      'thought':
          'Language carries soul — may your words today build bridges, not walls.'
    },
    '06-06': {
      'event': 'World Food Safety Day',
      'thought':
          'Clean food is a right — let\'s demand safety, not just availability.'
    },
    '06-07': {
      'event': 'World Oceans Day',
      'thought':
          'The ocean gives us life — let\'s return the favor with care, respect, and protection.'
    },
    '06-08': {
      'event': 'World Accreditation Day',
      'thought':
          'Accreditation builds trust — let\'s value standards that uphold safety and excellence.'
    },
    '06-09': {
      'event': 'World Art Nouveau Day',
      'thought':
          'Art Nouveau is elegance in motion — may creativity always bend toward beauty.'
    },
    '06-10': {
      'event': 'World Gin Day',
      'thought':
          'Here\'s to the spirit of juniper and joy — raise a glass, responsibly.'
    },
    '06-11': {
      'event': 'World Day Against Child Labour',
      'thought':
          'No child should labor when they should be learning — let\'s end this injustice.'
    },
    '06-12': {
      'event': 'International Albinism Awareness Day',
      'thought':
          'Difference is beautiful — today, stand up for visibility, rights, and equality.'
    },
    '06-13': {
      'event': 'World Blood Donor Day',
      'thought':
          'A single donation can save three lives — be the reason someone gets another tomorrow.'
    },
    '06-14': {
      'event': 'World Elder Abuse Awareness Day',
      'thought':
          'Respect begins with awareness — let\'s protect the dignity of every elder.'
    },
    '06-15': {
      'event': 'International Day of Family Remittances',
      'thought':
          'Behind every rupee sent is a story of sacrifice and love — here\'s to unsung heroes.'
    },
    '06-16': {
      'event': 'World Day to Combat Desertification and Drought',
      'thought':
          'Land matters — let\'s combat desertification and protect life on Earth.'
    },
    '06-17': {
      'event': 'Sustainable Gastronomy Day',
      'thought':
          'Food can heal the planet — eat mindfully, waste less, and savor every bite.'
    },
    '06-18': {
      'event':
          'International Day for the Elimination of Sexual Violence in Conflict',
      'thought':
          'In times of conflict, sexual violence should never be another weapon — speak out, support survivors.'
    },
    '06-19': {
      'event': 'World Refugee Day',
      'thought':
          'A refugee is not a label — it\'s a story of courage, loss, and hope. Let\'s open hearts and borders.'
    },
    '06-20': {
      'event': 'International Day of Yoga',
      'thought':
          'Yoga is balance, breath, and becoming — may your day move with intention.'
    },
    '06-21': {
      'event': 'World Rainforest Day',
      'thought':
          'The rainforest breathes for us — protect its voice, respect its silence.'
    },
    '06-22': {
      'event': 'International Widows\' Day',
      'thought':
          'Widows deserve respect, support, and inclusion — may their stories not be forgotten.'
    },
    '06-23': {
      'event': 'World UFO Day',
      'thought':
          'Wondering is human — let your curiosity fly high today (even if it\'s aboard a UFO).'
    },
    '06-24': {
      'event': 'Day of the Seafarer',
      'thought':
          'To those who sail beyond borders — thank you for keeping the world connected.'
    },
    '06-25': {
      'event': 'International Day Against Drug Abuse and Illicit Trafficking',
      'thought':
          'Drugs destroy futures — let\'s protect childhood, possibility, and freedom from addiction.'
    },
    '06-26': {
      'event': 'World Microbiome Day',
      'thought':
          'Your gut is your second brain — nurture it, and it\'ll nurture you.'
    },
    '06-27': {
      'event': 'World Brain Tumor Day',
      'thought':
          'Awareness leads to hope — let\'s support those affected by brain tumors.'
    },
    '06-28': {
      'event': 'World Scleroderma Day',
      'thought':
          'Scleroderma may be rare, but its impact is real — every story deserves to be heard.'
    },
    '06-29': {
      'event': 'World Asteroid Day',
      'thought':
          'Eyes to the skies — asteroids remind us that space is vast, and life is fragile.'
    },
    '06-30': {
      'event': 'World Architecture Day',
      'thought':
          'Great design is more than buildings — it\'s creating spaces that inspire and include.'
    },
// July Special Days Data
    '07-01': {
      'event': 'World UFO Day',
      'thought':
          'Curiosity about the unknown fuels imagination — let wonder lead the way.'
    },
    '07-02': {
      'event': 'International Plastic Bag Free Day',
      'thought':
          'Every plastic bag avoided is a silent win for our planet — choose wisely, live lightly.'
    },
    '07-03': {
      'event': 'World Zoonoses Day',
      'thought':
          'Zoonoses remind us that animal health and human health are deeply intertwined.'
    },
    '07-04': {
      'event': 'World Wildlife Conservation Day',
      'thought':
      'Every species saved is a step toward balance — protect wildlife, protect our future.'
    },
    '07-05': {
      'event': 'World Drought Day',
      'thought':
      'Droughts affect millions — let’s conserve water and support affected communities.'
    },
    '07-06': {
      'event': 'World Chocolate Day',
      'thought':
      'Some moments are best savored — today, let chocolate bring a little happiness.'
    },
    '07-07': {
      'event': 'World Allergy Day',
      'thought':
      'Allergies may be invisible, but empathy shouldn\'t be — let\'s support with understanding.'
    },
    '07-08': {
      'event': 'World Health Day',
      'thought':
      'Health is the foundation of possibility — protect it, nurture it, celebrate it.'
    },
    '07-09': {
      'event': 'World Population Day',
      'thought':
          'A growing population brings growing responsibility — let\'s plan with care and compassion.'
    },
    '07-10': {
      'event': 'World Population Day',
      'thought': 'Our numbers increase — let our empathy grow alongside.'
    },
    '07-11': {
      'event': 'World Paper Bag Day',
      'thought':
          'Small choices, like using paper, have big impacts — be the reason the planet smiles.'
    },
    '07-12': {
      'event': 'World Rock Day',
      'thought':
          'Turn up the music, feel the pulse — every day can be a rock anthem of hope.'
    },
    '07-13': {
      'event': 'World Chimpanzee Day',
      'thought':
          '98% similar to us — yet wholly wild. Let\'s protect our chimpanzee cousins.'
    },
    '07-14': {
      'event': 'World Youth Skills Day',
      'thought':
          'Skills empower — let\'s give young people the tools to shape their own future.'
    },
    '07-15': {
      'event': 'World Snake Day',
      'thought':
          'Snakes are symbols of transformation — respect all creatures, not just the cuddly ones.'
    },
    '07-16': {
      'event': 'World Emoji Day',
      'thought':
          'Even tiny icons can carry big emotions — send joy, not just texts.'
    },
    '07-17': {
      'event': 'Nelson Mandela International Day',
      'thought':
          'Lead with dignity, fight with love — honor Mandela by being the change.'
    },
    '07-18': {
      'event': 'World Listening Day',
      'thought':
          'True listening is rare — offer it often, and build stronger human bridges.'
    },
    '07-19': {
      'event': 'World Chess Day',
      'thought':
          'Every move counts — strategy, patience, and courage shape every game.'
    },
    '07-20': {
      'event': 'World Brain Day',
      'thought':
          'Your brain is brilliant — care for it like it powers your whole universe (because it does!).'
    },
    '07-21': {
      'event': 'World Fragile X Syndrome Day',
      'thought':
          'Awareness opens hearts — let\'s support every family touched by Fragile X.'
    },
    '07-22': {
      'event': 'World Whale and Dolphin Day',
      'thought':
          'Beneath the waves swim stories of grace — may whales and dolphins always roam free.'
    },
    '07-23': {
      'event': 'World Day for International Justice',
      'thought':
          'Justice isn\'t just a courtroom ideal — it\'s a daily promise of fairness.'
    },
    '07-24': {
      'event': 'World Drowning Prevention Day',
      'thought':
          'Water safety saves lives — let\'s raise awareness and make every swim safer.'
    },
    '07-25': {
      'event': 'World Day for the Conservation of the Mangrove Ecosystem',
      'thought':
          'Mangroves are nature\'s coastal defenders — let\'s preserve these unsung heroes.'
    },
    '07-26': {
      'event': 'World Head and Neck Cancer Day',
      'thought':
          'Early detection, open conversation — together we can change the cancer story.'
    },
    '07-27': {
      'event': 'World Hepatitis Day',
      'thought':
          'Hepatitis can be silent, but your awareness can be powerful — learn, share, support.'
    },
    '07-28': {
      'event': 'World Tiger Day',
      'thought':
          'A tiger\'s roar belongs in the wild — let\'s protect these majestic creatures.'
    },
    '07-29': {
      'event': 'International Day of Friendship',
      'thought':
          'Friendship crosses boundaries, heals wounds, and brings light — cherish yours today.'
    },
    '07-30': {
      'event': 'World Ranger Day',
      'thought':
          'Rangers walk the frontlines of conservation — honor their courage and commitment.'
    },
    '07-31': {
      'event': 'World Lung Cancer Day',
      'thought':
          'Awareness saves lives — early detection of lung cancer begins with open conversations.'
    },
// August Special Days Data
    '08-01': {
      'event': 'World Breastfeeding Day',
      'thought':
          'Breastfeeding is nature\'s love language — nourishing bodies and bonding souls.'
    },
    '08-02': {
      'event': 'World Watermelon Day',
      'thought':
          'Juicy, sweet, and full of summer joy — celebrate the simple magic of watermelon.'
    },
    '08-03': {
      'event': 'World Wide Web Day',
      'thought':
          'The web connects us all — let\'s use it to build bridges, not walls.'
    },
    '08-04': {
      'event': 'World Traffic Light Day',
      'thought':
          'Even the red light serves a purpose — sometimes, stopping is progress.'
    },
    '08-05': {
      'event': 'World Wide Web Day',
      'thought':
          'The internet has reshaped humanity — let\'s make it a space for growth and good.'
    },
    '08-06': {
      'event': 'World Elephant Day',
      'thought':
          'Majestic and endangered — let\'s ensure elephants roam free for generations to come.'
    },
    '08-07': {
      'event': 'World Cat Day',
      'thought':
          'Independent, mysterious, and full of grace — cats remind us to embrace our uniqueness.'
    },
    '08-08': {
      'event': 'International Day of the World\'s Indigenous Peoples',
      'thought':
          'Indigenous wisdom is rooted in harmony — listen, learn, and uplift their voices.'
    },
    '08-09': {
      'event': 'World Lion Day',
      'thought':
          'Lions teach us the strength of pride — protect the wild, protect our heritage.'
    },
    '08-10': {
      'event': 'World Steelpan Day',
      'thought':
          'Let the beat of the steelpan echo freedom, joy, and Caribbean soul.'
    },
    '08-11': {
      'event': 'International Youth Day',
      'thought':
          'Youth carry the blueprint of tomorrow — empower them with trust and opportunity.'
    },
    '08-12': {
      'event': 'World Organ Donation Day',
      'thought':
          'To give life beyond your own is the ultimate act of kindness — be an organ donor.'
    },
    '08-13': {
      'event': 'World Lizard Day',
      'thought':
          'Even the smallest creature has a role — let\'s protect biodiversity in every form.'
    },
    '08-14': {
      'event': 'World Honey Bee Day',
      'thought':
          'Buzzing with purpose — bees keep nature in balance. Let\'s give them a fighting chance.'
    },
    '08-15': {
      'event': 'World Elephant Day',
      'thought':
          'Their tusks don\'t belong on trophies — celebrate elephants alive and wild.'
    },
    '08-16': {
      'event': 'World Honeybee Day',
      'thought':
          'A world without bees is a world without fruit — protect our pollinators.'
    },
    '08-17': {
      'event': 'World Water Week',
      'thought':
          'Water connects every drop of life — conserve it like our future depends on it (because it does).'
    },
    '08-18': {
      'event': 'World Humanitarian Day',
      'thought':
          'In times of crisis, humanitarians are hope in motion — honor their courage today.'
    },
    '08-19': {
      'event': 'World Mosquito Day',
      'thought':
          'Tiny but mighty — mosquitoes remind us that prevention saves lives.'
    },
    '08-20': {
      'event':
          'International Day of Remembrance and Tribute to the Victims of Terrorism',
      'thought':
          'We remember the innocent lives lost to terror — may peace always be our answer.'
    },
    '08-21': {
      'event': 'World Plant Milk Day',
      'thought':
          'Plant-based, powerful, and kinder to the Earth — raise a glass to plant milk.'
    },
    '08-22': {
      'event':
          'International Day for the Remembrance of the Slave Trade and its Abolition',
      'thought':
          'Remember the pain, honor the resilience — commit to a future free from slavery.'
    },
    '08-23': {
      'event': 'World Day for the End of Speciesism',
      'thought': 'All beings deserve dignity — choose compassion over cruelty.'
    },
    '08-24': {
      'event': 'World Whisky Day',
      'thought':
          'A toast to tradition, stories, and celebration — sip responsibly, laugh freely.'
    },
    '08-25': {
      'event': 'World Dog Day',
      'thought':
          'Loyal and loving — dogs make life infinitely better. Give yours an extra treat today.'
    },
    '08-26': {
      'event': 'World Banana Day',
      'thought':
          'Full of potassium and personality — bananas bring a smile to any day.'
    },
    '08-27': {
      'event': 'World Red Wine Day',
      'thought':
          'Rich, bold, and best when shared — wine brings people together. Cheers to connection.'
    },
    '08-28': {
      'event': 'International Day against Nuclear Tests',
      'thought':
          'Let us strive for a world where peace, not power, defines strength.'
    },
    '08-29': {
      'event': 'International Day of the Victims of Enforced Disappearances',
      'thought':
          'For every story lost in silence, raise your voice for truth and remembrance.'
    },
    '08-30': {
      'event': 'World Blog Day',
      'thought':
          'One blog can spark a movement — share your voice, someone needs to hear it.'
    },
    '08-31': {
      'event': 'World Letter Writing Day',
      'thought':
          'A handwritten note is a tiny treasure — revive the art of writing, one word at a time.'
    },
// September Special Days Data
    '09-01': {
      'event': 'World Coconut Day',
      'thought':
          'Crack one open, savor the flavor — coconuts are nature\'s tropical gift.'
    },
    '09-02': {
      'event': 'World Beard Day',
      'thought':
          'Beards aren\'t just hair — they\'re personality statements. Celebrate the fuzz!'
    },
    '09-03': {
      'event': 'World Wildlife Day',
      'thought':
          'From forests to seas, the wild needs our protection — act today for tomorrow.'
    },
    '09-04': {
      'event': 'International Day of Charity',
      'thought':
          'True charity uplifts with dignity — even a small act can change a life.'
    },
    '09-05': {
      'event': 'World Coconut Day',
      'thought':
          'Tropical, versatile, and life-sustaining — celebrate the wonder of coconuts.'
    },
    '09-06': {
      'event': 'World Duchenne Awareness Day',
      'thought':
          'Duchenne may be rare, but compassion should never be — spread strength and support.'
    },
    '09-07': {
      'event': 'International Literacy Day',
      'thought':
          'Literacy unlocks the world — let\'s make reading a right, not a privilege.'
    },
    '09-08': {
      'event': 'World Suicide Prevention Day',
      'thought':
          'Reach out, speak up, listen deeply — your support could save a life.'
    },
    '09-09': {
      'event': 'World Suicide Prevention Day',
      'thought':
          'Silence can be heavy — let\'s lighten it with empathy, awareness, and care.'
    },
    '09-10': {
      'event': 'World First Aid Day',
      'thought': 'First aid is first love — learn it, use it, save lives.'
    },
    '09-11': {
      'event': 'United Nations Day for South-South Cooperation',
      'thought':
          'Global solidarity begins with mutual respect — cooperate, don\'t compete.'
    },
    '09-12': {
      'event': 'World Sepsis Day',
      'thought':
          'One infection, one response — early treatment saves lives. Know the signs of sepsis.'
    },
    '09-13': {
      'event': 'World Blood Cancer Day',
      'thought':
          'Every drop of blood tells a story — let\'s write one of survival and hope.'
    },
    '09-14': {
      'event': 'International Day of Democracy',
      'thought':
          'Democracy is not just a vote — it\'s your voice, your power, your responsibility.'
    },
    '09-15': {
      'event': 'International Day for the Preservation of the Ozone Layer',
      'thought': 'Ozone is Earth\'s sunscreen — let\'s not burn our future.'
    },
    '09-16': {
      'event': 'World Patient Safety Day',
      'thought':
          'Safety in care builds trust in healing — let\'s prioritize patient well-being.'
    },
    '09-17': {
      'event': 'World Water Monitoring Day',
      'thought':
          'The clearer the water, the stronger the future — protect every drop.'
    },
    '09-18': {
      'event': 'World Cleanup Day',
      'thought':
          'It takes a community to clean a planet — pitch in, every action matters.'
    },
    '09-19': {
      'event': 'World Rhino Day',
      'thought':
          'Let rhinos thrive, not just survive — protect them from the brink of extinction.'
    },
    '09-20': {
      'event': 'International Day of Peace',
      'thought': 'Peace isn\'t passive — it\'s a choice we make every day.'
    },
    '09-21': {
      'event': 'World Car Free Day',
      'thought':
          'One day without cars, one breath closer to cleaner skies — go car-free today.'
    },
    '09-22': {
      'event': 'International Day of Sign Languages',
      'thought':
          'Sign language is a voice — let\'s make sure it\'s heard and respected.'
    },
    '09-23': {
      'event': 'World Rivers Day',
      'thought':
          'Rivers carry stories, life, and future — keep them flowing, keep them clean.'
    },
    '09-24': {
      'event': 'World Pharmacists Day',
      'thought':
          'Pharmacists don\'t just fill prescriptions — they help people heal. Thank them today.'
    },
    '09-25': {
      'event': 'International Day for the Total Elimination of Nuclear Weapons',
      'thought':
          'A world without nuclear weapons is not a dream — it\'s a global commitment.'
    },
    '09-26': {
      'event': 'World Tourism Day',
      'thought':
          'Let every journey expand your soul — travel with curiosity and kindness.'
    },
    '09-27': {
      'event': 'World Rabies Day',
      'thought':
          'Vaccination saves lives — let\'s stop rabies before it starts.'
    },
    '09-28': {
      'event': 'International Day of Awareness of Food Loss and Waste',
      'thought':
          'Food waste is a choice — make yours one that nourishes the planet.'
    },
    '09-29': {
      'event': 'International Translation Day',
      'thought':
          'Translation brings the world closer — celebrate those who build bridges with words.'
    },
    '09-30': {
      'event': 'International Day of Older Persons',
      'thought':
          'Aging is a privilege — let\'s honor the wisdom and contributions of our older generations.'
    },
// October Special Days Data
    '10-01': {
      'event': 'International Day of Non-Violence',
      'thought':
          'Peace begins with a choice — choose non-violence, choose progress.'
    },
    '10-02': {
      'event': 'World Habitat Day',
      'thought':
          'A better world starts with better homes — let\'s build sustainable, inclusive habitats for all.'
    },
    '10-03': {
      'event': 'World Animal Day',
      'thought':
          'Animals speak the language of love and balance — let\'s protect every species we share the planet with.'
    },
    '10-04': {
      'event': 'World Teachers\' Day',
      'thought':
          'Teachers don\'t just teach — they inspire, nurture, and shape futures.'
    },
    '10-05': {
      'event': 'World Cerebral Palsy Day',
      'thought':
          'Cerebral palsy may affect movement, but it can never limit dreams — let\'s build an inclusive world.'
    },
    '10-06': {
      'event': 'World Architecture Day',
      'thought':
          'Every building begins with a vision — architecture is where art meets impact.'
    },
    '10-07': {
      'event': 'World Octopus Day',
      'thought':
          'Mysterious, intelligent, and graceful — octopuses remind us how incredible nature really is.'
    },
    '10-08': {
      'event': 'World Post Day',
      'thought':
          'A letter, a message, a moment — celebrate the power of human connection through post.'
    },
    '10-09': {
      'event': 'World Mental Health Day',
      'thought':
          'Mental health matters — let\'s talk, listen, and support each other without judgment.'
    },
    '10-10': {
      'event': 'International Day of the Girl Child',
      'thought':
          'Empowered girls change the world — educate, uplift, and amplify their voices.'
    },
    '10-11': {
      'event': 'World Arthritis Day',
      'thought':
          'Strong joints, stronger awareness — support those living with arthritis with empathy and care.'
    },
    '10-12': {
      'event': 'International Day for Disaster Risk Reduction',
      'thought':
          'Preparedness saves lives — disaster risk reduction starts with awareness.'
    },
    '10-13': {
      'event': 'World Standards Day',
      'thought':
          'Standards shape progress — celebrate the silent frameworks behind innovation and safety.'
    },
    '10-14': {
      'event': 'International Day of Rural Women',
      'thought':
          'Rural women grow food, raise communities, and hold the world together — recognize their strength.'
    },
    '10-15': {
      'event': 'World Food Day',
      'thought':
          'Good food feeds more than the body — it nourishes health, community, and culture.'
    },
    '10-16': {
      'event': 'International Day for the Eradication of Poverty',
      'thought':
          'Poverty isn\'t just a statistic — it\'s a call to action for dignity and equality.'
    },
    '10-17': {
      'event': 'World Vasectomy Day',
      'thought':
          'A small cut, a big impact — vasectomy is a shared responsibility in family planning.'
    },
    '10-18': {
      'event': 'World Pediatric Bone and Joint Day',
      'thought':
          'Tiny bones, big dreams — let\'s advocate for stronger pediatric orthopedic care.'
    },
    '10-19': {
      'event': 'World Statistics Day',
      'thought':
          'Every number tells a story — statistics help us see the bigger picture, clearly and truthfully.'
    },
    '10-20': {
      'event': 'World Iodine Deficiency Day',
      'thought':
          'Iodine is tiny but vital — a healthy future starts with the right nutrition.'
    },
    '10-21': {
      'event': 'World Stuttering Awareness Day',
      'thought':
          'Voices matter, no matter how they sound — support awareness and empathy for stuttering.'
    },
    '10-22': {
      'event': 'World Snow Leopard Day',
      'thought':
          'Elusive and majestic — let\'s protect snow leopards and the fragile ecosystems they guard.'
    },
    '10-23': {
      'event': 'United Nations Day',
      'thought':
          'Together under one flag — celebrate the vision and efforts of the United Nations for global peace.'
    },
    '10-24': {
      'event': 'World Pasta Day',
      'thought':
          'From simple noodles to shared joy — pasta brings the world to the table.'
    },
    '10-25': {
      'event': 'World Day for Audiovisual Heritage',
      'thought':
          'Preserve the sounds and sights of our times — celebrate the value of audiovisual heritage.'
    },
    '10-26': {
      'event': 'World Day for Audiovisual Heritage',
      'thought':
          'Stories saved are stories shared — honor the history stored in every frame and sound.'
    },
    '10-27': {
      'event': 'World Animation Day',
      'thought':
          'Animation brings imagination to life — celebrate the magic of movement and storytelling.'
    },
    '10-28': {
      'event': 'World Stroke Day',
      'thought':
          'Time matters — learn the signs of stroke and act FAST to save a life.'
    },
    '10-29': {
      'event': 'World Thrift Day',
      'thought':
          'Thrift is wisdom in action — make saving a habit and sustainability a lifestyle.'
    },
    '10-30': {
      'event': 'World Cities Day',
      'thought':
          'A smart city isn\'t just built — it\'s lived in with purpose, resilience, and joy.'
    },
    '10-31': {
      'event': 'World Vegan Day',
      'thought':
          'Vegan choices can nourish both you and the planet — celebrate mindful living.'
    },
  };

  String _getCurrentDateKey() {
    final now = DateTime.now();
    final month = now.month.toString().padLeft(2, '0');
    final day = now.day.toString().padLeft(2, '0');
    return '$month-$day';
  }

  String _getMessage() {
    if (customMessage != null) {
      return customMessage!;
    }

    final dateKey = _getCurrentDateKey();
    final specialDay = _specialDays[dateKey];

    if (specialDay != null) {
      return specialDay['event']!;
    }

    return "Have a wonderful day!";
  }

  String? _getSpecialThought() {
    if (!showSpecialThoughts) return null;

    final dateKey = _getCurrentDateKey();
    final specialDay = _specialDays[dateKey];

    return specialDay?['thought'];
  }

  @override
  Widget build(BuildContext context) {
    final message = _getMessage();
    final specialThought = _getSpecialThought();

    return Positioned(
      bottom: 20,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
          padding: const EdgeInsets.symmetric(
              horizontal: AppSpacing.md, vertical: AppSpacing.xs),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(AppSpacing.xs),
            border: Border.all(color: Colors.grey.shade400, width: 1),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Main event message
              Text(
                message,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.titleMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
                // style: const TextStyle(
                //   fontSize: 14,
                //   fontWeight: FontWeight.w500,
                //   color: Colors.black,
                // ),
                textAlign: TextAlign.center,
              ),

              // Optional thought (italic, subtle tone)
              if (specialThought != null &&
                  specialThought!.trim().isNotEmpty) ...[
                const SizedBox(height: AppSpacing.xs),
                Container(
                  padding: const EdgeInsets.all(AppSpacing.xs),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(AppSpacing.xxs),
                    border: Border.all(color: Colors.grey.shade300, width: 1),
                  ),
                  child: Text(
                    specialThought!,
                     style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.titleSmall(context),
                  fontWeight: FontWeight.w400,
                  color: Colors.grey.shade700,
                ),
                    // style: TextStyle(
                    //   fontSize: 12,
                    //   fontWeight: FontWeight.w400,
                    //   color: Colors.grey.shade700,
                    //   fontStyle: FontStyle.italic,
                    // ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

// Helper class to manage and load special days data
class SpecialDaysManager {
  static Map<String, Map<String, String>> getAllSpecialDays() {
    return DayCaller._specialDays;
  }

  static Map<String, String>? getSpecialDayForDate(DateTime date) {
    final month = date.month.toString().padLeft(2, '0');
    final day = date.day.toString().padLeft(2, '0');
    final dateKey = '$month-$day';
    return DayCaller._specialDays[dateKey];
  }

  static bool hasSpecialDay(DateTime date) {
    return getSpecialDayForDate(date) != null;
  }

  static String getEventForDate(DateTime date) {
    final specialDay = getSpecialDayForDate(date);
    return specialDay?['event'] ?? 'Have a wonderful day!';
  }

  static String? getThoughtForDate(DateTime date) {
    final specialDay = getSpecialDayForDate(date);
    return specialDay?['thought'];
  }
}

// Example usage widget
class DayCallerExample extends StatefulWidget {
  @override
  _DayCallerExampleState createState() => _DayCallerExampleState();
}

class _DayCallerExampleState extends State<DayCallerExample> {
  bool _showThoughts = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Day Caller Demo'),
        backgroundColor: Colors.blue.shade50,
      ),
      body: Stack(
        children: [
          // Your main content here
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text(
                  'Your App Content',
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _showThoughts = !_showThoughts;
                    });
                  },
                  child:
                      Text(_showThoughts ? 'Hide Thoughts' : 'Show Thoughts'),
                ),
                const SizedBox(height: 20),
                // Display current date's special day info
                if (SpecialDaysManager.hasSpecialDay(DateTime.now()))
                  Card(
                    margin: const EdgeInsets.all(AppSpacing.md),
                    child: Padding(
                      padding: const EdgeInsets.all(AppSpacing.md),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Today\'s Special Day:',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.shade700,
                            ),
                          ),
                          const SizedBox(height: AppSpacing.xs),
                          Text(
                            SpecialDaysManager.getEventForDate(DateTime.now()),
                            style: const TextStyle(fontSize: 14),
                          ),
                          if (SpecialDaysManager.getThoughtForDate(
                                  DateTime.now()) !=
                              null) ...[
                            const SizedBox(height: AppSpacing.xs),
                            Text(
                              SpecialDaysManager.getThoughtForDate(
                                  DateTime.now())!,
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade600,
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),

          // The DayCaller widget
          DayCaller(
            showSpecialThoughts: _showThoughts,
          ),
        ],
      ),
    );
  }
}
