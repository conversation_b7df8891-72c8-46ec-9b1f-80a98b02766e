{"app": {"name": "NSL", "version": "Version {version}"}, "common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "close": "Close", "back": "Back", "next": "Next", "submit": "Submit", "required": "Required", "optional": "Optional", "search": "Search", "filter": "Filter", "sort": "Sort", "view": "View", "create": "Create", "update": "Update", "details": "Details", "noData": "No data available", "retry": "Retry", "ok": "OK", "enterValue": "Enter value", "pageNotFound": "Page Not Found", "pageNotFoundMessage": "The page {pageName} was not found.", "start": "Start", "notAvailable": "N/A"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "signUp": "Sign Up", "logout": "Logout", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "username": "User Name", "firstName": "First Name", "lastName": "Last Name", "fullName": "Full Name", "organization": "Organization", "role": "Role", "mobile": "Mobile", "rememberMe": "Remember Me", "forgotPassword": "Forgot Password?", "welcomeBack": "Welcome Back", "pleaseSignIn": "Please sign in to continue", "dontHaveAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?", "createAccount": "Create Account", "resetPassword": "Reset Password", "signIn": "Sign In", "alreadyRegistered": "Already Registered? Click", "notRegistered": "Not Registered? Click", "profilePicture": "Profile Picture", "uploadProfilePicture": "Upload Profile Picture", "registrationSuccess": "Your account has been created successfully. Please login with your credentials.", "validation": {"emailRequired": "Email is required", "emailInvalid": "Please enter a valid email address", "passwordRequired": "Password is required", "passwordTooShort": "Password must be at least 8 characters", "passwordsDoNotMatch": "Passwords do not match", "usernameRequired": "Username is required", "firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "organizationRequired": "Organization is required", "roleRequired": "Role is required", "mobileRequired": "Mobile number is required", "mobileInvalid": "Please enter a valid mobile number"}, "localAccount": "Local Account", "loggingOut": "Logging out...", "signOutOfAccount": "Sign out of your account", "loadingProfile": "Loading profile...", "refreshingToken": "Refreshing session...", "loadingAuthData": "Loading authentication data...", "authenticating": "Authenticating...", "registering": "Creating account...", "signingIn": "Signing in...", "signingOut": "Signing out..."}, "navigation": {"home": "Home", "chat": "Cha<PERSON>", "build": "Create", "transact": "Transaction", "myTransactions": "My Transactions", "settings": "Settings", "profile": "Profile", "logout": "Logout", "dashboard": "Dashboard", "components": "Components", "uiComponents": "UI Components", "drawer": {"appName": "NSL"}, "help": "Help", "logoutConfirmation": "Are you sure you want to logout?", "helpComingSoon": "Help section coming soon!", "widgetBinder": "Widget Binder", "code": "Code"}, "settings": {"title": "Settings", "theme": "Theme", "darkMode": "Dark Mode", "darkModeDescription": "Toggle between light and dark theme", "language": "Language", "languageDescription": "Change the application language", "notifications": "Notifications", "account": "Account", "about": "About", "help": "Help", "feedback": "<PERSON><PERSON><PERSON>", "saveSuccess": "Setting<PERSON> saved successfully", "saveChanges": "Save Changes", "appearance": "Appearance", "privacy": "Privacy", "advanced": "Advanced", "uiSettings": "UI Settings", "chatSettings": "<PERSON><PERSON>", "fontSize": "Font Size", "fontSizeDescription": "Adjust the size of text throughout the application", "uiDensity": "UI Density", "uiDensityDescription": "Adjust the spacing between UI elements", "showTimestamps": "Show Timestamps", "showTimestampsDescription": "Display timestamps for each message", "showReadReceipts": "Show Read Receipts", "showReadReceiptsDescription": "Let others know when you've read their messages", "sendMessageOnEnter": "Send Message on Enter", "sendMessageOnEnterDescription": "Press Enter to send messages instead of Shift+Enter", "version": "Version", "versionDescription": "Current application version", "light": "Light", "dark": "Dark", "system": "System", "comingSoon": "{feature} settings coming soon"}, "profile": {"title": "Profile", "editProfile": "Edit Profile", "changePassword": "Change Password", "personalInfo": "Personal Information", "contactInfo": "Contact Information", "saveChanges": "Save Changes", "discardChanges": "Discard Changes", "userInfoNotAvailable": "User information not available", "profileInformation": "Profile Information", "accountInformation": "Account Information", "refreshProfile": "Refresh Profile", "fullName": "Full Name", "username": "Username", "emailAddress": "Email Address", "mobileNumber": "Mobile Number", "role": "Role", "organization": "Organization", "userId": "User ID", "accountStatus": "Account Status", "tenantId": "Tenant ID", "roles": "Roles", "organizationUnits": "Organization Units", "authProvider": "Authentication Provider", "notProvided": "Not provided", "notAvailable": "Not available", "editProfileComingSoon": "Edit profile functionality coming soon", "viewProfileDetails": "View your profile details"}, "chat": {"newChat": "New Chat", "newConversation": "New Conversation", "typeMessage": "Type a message...", "send": "Send", "greeting": "How can I help you {greeting}?", "clearChat": "Clear Chat", "clearChatConfirmation": "Are you sure you want to clear the chat history?", "cancel": "Cancel", "clear": "Clear", "chatWithNSL": "Chat with NSL...", "fetchingAnswer": "NSL fetching answer", "conversations": "Conversations", "history": "History", "searchConversations": "Search conversations", "recentChats": "Recent Chats", "chatCount": "{count} chats", "noConversations": "No conversations yet", "noMessagesYet": "No messages yet", "rename": "<PERSON><PERSON>", "renameConversation": "Rename Conversation", "enterNewName": "Enter new name", "exportChat": "Export <PERSON>", "context": "Context", "aiAssistant": "AI Assistant", "currentConversation": "Current Conversation", "topic": "Topic:", "noSpecificTopic": "No specific topic detected", "keyPoints": "Key Points:", "noContextAvailable": "• No context available for this conversation", "relatedInformation": "Related Information", "noRelatedInformation": "No related information available", "microphonePermissionRequired": "Microphone Permission Required", "microphonePermissionMessage": "This app needs microphone access for text-to-speech functionality. Please grant microphone permission in your device settings.", "suggestions": {"codeReview": "Help me with code review", "explainConcept": "Explain this concept", "debugCode": "Debug my code"}}, "build": {"newSolution": "New Solution", "solutionName": "Solution Name", "description": "Description", "uploadFile": "Upload File", "createSolution": "Create Solution", "create": "Create", "newProject": "New Project", "backToCreateMenu": "Back to create menu", "clearChat": "Clear Chat", "clearChatConfirmation": "Are you sure you want to clear the chat history?", "noSolutions": "No solutions available", "createYourFirstSolution": "Create your first solution", "solutions": "Solutions", "solution": "Solution", "solutionDetails": "Solution Details", "exportYAML": "Export YAML", "rolesGreeting": "Great! Here is your Roles and it's use cases for this solution.", "entitiesGreeting": "Great! Here is your Entities and it's use cases for this solution.", "workflowGreeting": "Great! Here is your Workflow and it's use cases for this solution.", "suggestions": {"title": "Try asking NSL to:", "createWorkflow": "Create a workflow", "generateYAML": "Generate YAML", "buildSolution": "Build a solution"}, "examples": {"createWorkflow": "Create a workflow for processing customer orders", "generateYAML": "Generate YAML for a data transformation pipeline", "buildSolution": "Build a solution for inventory management"}}, "workflow": {"title": "Workflows", "createNew": "Create New Workflow", "createNewComingSoon": "Create new workflow - Coming soon", "workflowDetails": "Workflow Details", "workflowDetailsComingSoon": "Workflow details - Coming soon", "status": {"active": "Active", "draft": "Draft", "archived": "Archived", "completed": "Completed"}, "types": {"approvalWorkflow": "Approval Workflow", "documentProcessing": "Document Processing"}, "descriptions": {"approvalWorkflow": "Standard approval process with multiple steps", "documentProcessing": "Automated document processing and validation"}, "nodeDetails": "Node Details", "closeDetails": "Close details", "currentSolution": "Current Solution", "noActiveSolution": "No active solution", "components": "Components", "noComponentsDefined": "No components defined yet", "solutionTemplates": "Solution Templates", "templates": {"dataProcessing": "Data Processing Pipeline", "orderManagement": "Order Management System", "customerFeedback": "Customer Feedback Analysis"}}, "components": {"title": "UI Components", "richTextEditor": "Quill Rich Text Editor", "basicEditor": "Basic Editor with <PERSON><PERSON><PERSON>", "tryFormatting": "Try formatting this text using the toolbar above.", "startTyping": "Start typing here..."}, "dashboard": {"noNotifications": "No Notificataions", "noRecentActivity": "No recent activity", "notifications": "Notifications", "quickActions": "<PERSON><PERSON><PERSON><PERSON>", "recentActivity": "Recent Activity", "statistics": "Statistics", "title": "Dashboard", "viewAll": "View All", "welcome": "Welcome, {name}!"}, "transaction": {"globalObjectives": "Global Objectives", "localObjectives": "Local Objectives", "status": "Status", "id": "ID", "version": "Version", "viewDetails": "View Details", "startWorkflow": "Start Workflow", "completeWorkflow": "Complete Workflow", "noObjectives": "No objectives available", "transact": "Transact", "myTransactions": "My Transactions", "checkingExistingTransactions": "Checking for existing transactions...", "existingTransactions": "Existing Transactions", "existingTransactionsFor": "Existing transactions for {name}", "existingTransactionsQuestion": "There are existing transactions. Do you want to see them?", "noExistingTransactions": "No existing transactions found", "startNewTransactionPrompt": "Start a new transaction using the button below", "startNewTransaction": "Start New Transaction", "instanceId": "Instance ID", "created": "Created", "updated": "Updated", "resume": "Resume", "yes": "Yes", "no": "No", "data": "Data", "timestamp": "Timestamp", "clearChat": "Clear Chat", "clearChatConfirmation": "Are you sure you want to clear the chat history?", "completed": "COMPLETED", "pending": "PENDING", "failed": "FAILED", "greeting": "Hello, {name}", "welcomeMessage": "Welcome to the Transaction Center", "searchTransactions": "Search transactions...", "enterTransactionDetails": "Enter transaction details...", "errorLoadingData": "Error loading data: {error}", "objectiveId": "ID: {id}", "objectiveName": "Name: {name}", "objectiveStatus": "Status: {status}", "objectiveVersion": "Version: {version}", "objectiveDetails": "Objective Details", "startTransaction": "Start Transaction", "startNewTransactionWith": "Start a new transaction with {name}?", "errorLoadingTransactions": "Error loading transactions", "loadingTransactions": "Loading transactions...", "noTransactionsFound": "No transactions found", "noFilteredTransactionsFound": "No {status} transactions found", "all": "All", "newTransactionMessage": "New transaction form would go here.", "editTransactionMessage": "Edit transaction functionality would go here.", "resumeTransactionMessage": "This would navigate to the workflow detail screen to resume the transaction.", "workerId": "Worker ID: {id}", "workflowId": "Workflow ID", "dateTime": "Date & Time", "lastUpdated": "Last Updated", "updatedDate": "Updated: {date}", "formattedDate": "{date}", "totalLocalObjectives": "Total Local Objectives: {count}", "noLocalObjectiveDetails": "No local objective details available", "localObjective": "Local Objective", "loCount": "{count} LO{plural}", "selectItemForDetails": "Select an item to view details", "globalObjectiveDetails": "Global Objective Details", "transactionDetails": "Transaction Details", "groupedTransactionDetails": "Grouped Transaction Details", "tenantId": "Tenant ID", "resumeTransaction": "Resume Transaction"}, "home": {"typeYourReply": "Type your reply", "greeting": "Hi {name}, How can I help you?", "selectQuickMessage": "Please select a quick message type before sending", "selectQuickMessageHint": "Select a message type", "sendingMessage": "Sending message...", "loadingChatHistory": "Loading chat history...", "noChatHistory": "No chat history found", "askNSL": "Ask NSL", "nsl": "NSL", "solution": "Solution", "general": "General", "internet": "Internet", "discovery": "Discovery", "development": "Development", "totalDiscoveryProgress": "Total Discovery Progress", "businessRequirementDocument": "Business Requirement Document", "brdProcess": "BRD Process", "questionsRemaining": "Questions Remaining", "consumedTokens": "Consumed Tokens"}, "library": {"books": "12 Books", "objects": "102 Objects", "solutions": "35 Solutions", "agents": "10 Agents", "pageTitle": "My Books", "createButtonText": "Create Book"}, "sidemenu": {"chat": "Cha<PERSON>", "create": "Create", "myBusiness": "My Business", "home": "Home", "collections": "Collections", "solutions": "Solutions", "records": "Records", "myTransactions": "My Transactions", "calendar": "Calendar", "notifications": "Notifications", "nslToJavaCode": "NSL to Java Code", "multiMedia": "OCR", "myProfile": "My Profile", "maxNewPlan": "<PERSON> Plan", "viewPlan": "View Plan", "learnMore": "Learn More", "language": "Language", "getHelp": "Get Help", "settings": "Settings", "logout": "Logout", "instantFinancial": "Instant Financial"}, "bookdetails": {"createYourBook": "Create Your Book", "name": "Name", "description": "Description", "industry": "Industry", "descriptionAboutTheProject": "Description about the project", "start": "Start"}, "websolution": {"books": "12 Books", "objects": "102 Objects", "solutions": "35 Solutions", "pageTitle": "My Solution", "createButtonText": "Create Solution"}, "webobject": {"pageTitle": "My Objects", "createButtonText": "Create Object"}, "webagent": {"pageTitle": "Agents", "createButtonText": "Create Agent"}, "myBusinessHome": {"favourites": "Favourites", "recentUsedSolutions": "Recent Used Solutions"}, "myBusinessCollections": {"collections": "Collections"}, "myBusinessRecords": {"records": "Records"}, "uiControls": {"microphonePermissionRequired": "Microphone Permission Required", "appPermissionTextToSpeech": "This app needs microphone access for text-to-speech functionality.\n Please grant microphone permission in your device settings."}, "myBusinessSolutions": {"solutions": "Solutions", "start": "Start", "pending": "Pending"}, "greeting_monday_morning_first": "Good Morning and a Happy Monday!\nReady to build something amazing?", "greeting_monday_afternoon_first": "Good Afternoon! Beating the Monday blues?\nLet’s keep the momentum going.", "greeting_monday_evening_first": "Good Evening!\nBack to wrap up or try something new this Monday?", "greeting_monday_night_first": "Burning the Monday midnight oil?\nLet’s make it count.", "greeting_monday_early_morning_first": "Starting your Monday early?\nLet’s get a head way.", "greeting_monday_morning_repeat": "Happy Monday morning!\nGot something exciting in mind? Let’s get started.", "greeting_monday_afternoon_repeat": "Midday Monday check-in — still building?\nWe’ve got your back.", "greeting_monday_evening_repeat": "Good Evening!\nLet’s give this Monday a strong finish.", "greeting_monday_night_repeat": "Still building late on a Monday?\nLet’s end the day with impact.", "greeting_monday_early_morning_repeat": "Up early this Monday?\nLet’s put that head start to good use.", "greeting_tuesday_morning_first": "Good Morning and a terrific Tuesday {name}!\nWhat are we creating today?", "greeting_tuesday_afternoon_first": "It’s Lucky Tuesday afternoon, {name}!\nWhat are we building?", "greeting_tuesday_evening_first": "Good Evening {name}!\nLet’s wind your Tuesday with something new?", "greeting_tuesday_night_first": "Hitting the Tuesday nightcap?\nLets close the day in style!", "greeting_tuesday_early_morning_first": "Giving an early start to your Tuesday?\nLet’s plan the catch!", "greeting_tuesday_morning_repeat": "Good Morning, {name}!\nLet’s take Tuesday by storm.", "greeting_tuesday_afternoon_repeat": "Tuesday’s in full swing, {name}!\nIt’s the perfect time to build something great.", "greeting_tuesday_evening_repeat": "Evening, {name}!\nOne more build before Tuesday winds down?", "greeting_tuesday_night_repeat": "Working late this Tuesday?\nYou’re on a roll — let’s keep it going.", "greeting_tuesday_early_morning_repeat": "Early Tuesday start? Great minds rise early.\nLet’s plan it Big!", "greeting_wednesday_morning_first": "Good Morning and a wonderful Wednesday to you!\nLets start the midweek magic.", "greeting_wednesday_afternoon_first": "Good Afternoon!\nLet’s build something magical today!", "greeting_wednesday_evening_first": "Winding the Wednesday evening?\nYou're in good company.", "greeting_wednesday_night_first": "Rediscovering your Wednesday night?\nLet’s make it count!", "greeting_wednesday_early_morning_first": "Getting an early start?\nLets make your Wednesday rock!", "greeting_wednesday_morning_repeat": "Happy Wednesday Morning!\nLet’s light up the midweek with your next build.", "greeting_wednesday_afternoon_repeat": "Happy Wednesday!\nWhat are we creating this afternoon?", "greeting_wednesday_evening_repeat": "Unwinding on a Wednesday with a Project?\nWe’re right here with you.", "greeting_wednesday_night_repeat": "Up late on a Wednesday?\nLet’s do something worth staying up for.", "greeting_wednesday_early_morning_repeat": "Midweek early jumpstart?\nLet’s get it rocking!", "greeting_thursday_morning_first": "Good Morning! It’s Thursday already, {name}!\nLet’s make something quirky today.", "greeting_thursday_afternoon_first": "Happy Thursday afternoon {name}!\nWhat are we building today?", "greeting_thursday_evening_first": "Winding your Thursday with an evening project?\nLet’s get it started!", "greeting_thursday_night_first": "A working Thursday night?\nLet’s keep it rocking!", "greeting_thursday_early_morning_first": "Breaking the dawn with early Thursday work?\nLet’s get it going!", "greeting_thursday_morning_repeat": "Happy Thursday morning! Ready to build something delightfully different?", "greeting_thursday_afternoon_repeat": "Happy Thursday afternoon!\nReady to turn thoughts into Solutions?", "greeting_thursday_evening_repeat": "Evening projects on a Thursday?\nLet’s power through together.", "greeting_thursday_night_repeat": "Fueling your Thursday night hustle?\nCount us in.", "greeting_thursday_early_morning_repeat": "Up early this Thursday?\nLet’s turn the quiet into steady momentum.", "greeting_friday_morning_first": "Good Morning and a fabulous Friday to you!\nLet’s start it strong.", "greeting_friday_afternoon_first": "Good Afternoon! Friday vibes in the air?\nLet’s give shape to the fun ideas.", "greeting_friday_evening_first": "Planning a quirky Friday evening?\nLet’s build something fun together!", "greeting_friday_night_first": "Burning your Friday night fuel?\nLet’s channel the spark!", "greeting_friday_early_morning_first": "Welcome to an early FriYay!\nLet’s begin the fun projects!", "greeting_friday_morning_repeat": "Happy Friday morning!\nGot a fun build in mind? Let’s make it real.", "greeting_friday_afternoon_repeat": "Ideas don’t take Fridays off.\nLet’s build a weekend rocker!", "greeting_friday_evening_repeat": "Friday evening and still building?\nYou’ve got that spark — let’s light it up.", "greeting_friday_night_repeat": "Up late on a Friday with ideas?\nLet’s shape those into Solutions!", "greeting_friday_early_morning_repeat": "Kicking off an early Friday!\nAre we making something new?", "greeting_saturday_morning_first": "It’s Saturday and you’re here?\nThat’s real passion.\nLet’s build together.", "greeting_saturday_afternoon_first": "Good Afternoon!\nStill going strong this Saturday?\nWe’re with you every step of the way.", "greeting_saturday_evening_first": "Saturday evening and still building?\nThat’s the spirit!\nLet’s make it worth it.", "greeting_saturday_night_first": "Burning the Saturday midnight oil?\nWe’re right here with you.", "greeting_saturday_early_morning_first": "Up early on a Saturday?\nThat’s next-level dedication!\nLet’s create something great.", "greeting_saturday_morning_repeat": "Good morning! Showing up on a Saturday?\nThat’s the spirit we admire.", "greeting_saturday_afternoon_repeat": "Weekend hustles are impressive!\nWe’re all in with you...", "greeting_saturday_evening_repeat": "Innovating on a Saturday evening?\nThat even rhymed — and it’s perfect.", "greeting_saturday_night_repeat": "Working this late on a Saturday night?\nYou inspire us!", "greeting_saturday_early_morning_repeat": "Early morning grind on a Saturday?\nYou’re setting high standards!", "greeting_sunday_morning_first": "It’s Sunday, and you showed up.\nThat’s deep commitment.\nLet’s shape something meaningful.", "greeting_sunday_afternoon_first": "On a build streak this Sunday afternoon?\nWe’re right with you.", "greeting_sunday_evening_first": "Still working this Sunday evening?\nYou bring the passion, we’ll bring the backup.", "greeting_sunday_night_first": "Sunday night and still on the grind?\nThat’s purpose-driven work we love to back.", "greeting_sunday_early_morning_first": "Sunday projects?\nLet's keep this dedication going!", "greeting_sunday_morning_repeat": "Fueling up your Sunday morning with creation?\nWe’re here for it.", "greeting_sunday_afternoon_repeat": "Still building on a Sunday afternoon?\nYou’ve piqued our interest!", "greeting_sunday_evening_repeat": "Sunday projects in progress?\nWe’re intrigued — tell us more!", "greeting_sunday_night_repeat": "Fun with builds this late on a Sunday night?\nYou’ve definitely got our attention!", "greeting_sunday_early_morning_repeat": "Up this early on a Sunday?\n{name}, that’s devotion we didnt expect!!"}