import 'dart:math' show min, max;
import 'package:flutter/material.dart';
import 'package:nsl/providers/web_book_solution_provider.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/hover_nav_item.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';
import '../../../../../theme/app_colors.dart';

class WebAddModulesPage extends StatelessWidget {
  const WebAddModulesPage({super.key});

  Widget _buildScaffold(
      BuildContext context, WebBookSolutionProvider provider) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      body: Padding(
        padding:
            const EdgeInsets.only(left: 94, right: 94, bottom: 0.0, top: 0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 12,
              child: Column(
                children: [
                  Row(
                    children: [
                      _buildHeader(context),
                      // Expanded(
                      //   child: Center(child: HoverNavItems()),
                      // ),
                      // const SizedBox(width: 45.6),
                    ],
                  ),
                  Expanded(
                    child: Padding(
                      padding:
                          const EdgeInsets.symmetric(vertical: AppSpacing.lg),
                      child: _buildContent(context, provider),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(child: SizedBox()),
          ],
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context, WebBookSolutionProvider provider) {
    return Column(
      children: [
        Expanded(
          child: _buildAddModulesContent(context, provider),
        ),
        // Bottom buttons section
        _buildBottomButtons(context),
      ],
    );
  }

  Widget _buildBottomButtons(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(top: 16, bottom: 6),
      decoration: BoxDecoration(
        color: const Color(0xFFF5F7FA),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // Cancel button
          MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: () => Navigator.pop(context),
              child: Container(
                height: 36,
                padding: const EdgeInsets.symmetric(horizontal: 24),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(color: Colors.grey.shade400),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Center(
                  child: Text(
                    'Cancel',
                    style: FontManager.getCustomStyle(
                      color: Colors.black,
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      fontWeight: FontManager.medium,
                    ),
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          // Done button
          MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: () => Navigator.pop(context),
              child: Container(
                height: 36,
                padding: const EdgeInsets.symmetric(horizontal: 24),
                decoration: BoxDecoration(
                  color: const Color(0xFF0058FF),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: TextButton(
                  onPressed: () => Navigator.pop(context),
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  child: Text(
                    'Done',
                    style: FontManager.getCustomStyle(
                      color: Colors.white,
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      fontWeight: FontManager.medium,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<WebBookSolutionProvider>(
      builder: (context, provider, child) {
        return _buildScaffold(context, provider);
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      height: AppSpacing.xxl,
      padding: const EdgeInsets.only(right: 16),
      decoration: const BoxDecoration(
        color: Colors.transparent,
      ),
      child: MouseRegion(
        cursor: SystemMouseCursors.click,
        child: InkWell(
          borderRadius: BorderRadius.circular(4),
          onTap: () {
            Provider.of<WebHomeProvider>(context, listen: false)
                .currentScreenIndex = ScreenConstants.webInsideBookModule;
          },
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(
                Icons.arrow_back,
                color: Colors.grey,
                size: AppSpacing.md,
              ),
              SizedBox(width: AppSpacing.xs),
              Text(
                'Previous Page',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontManager.medium,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? AppColors.textPrimaryDark
                      : AppColors.textPrimaryLight,
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAddModulesContent(
      BuildContext context, WebBookSolutionProvider provider) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Stack(
        children: [
          Column(
            children: [
              // Header row
              Container(
                height: AppSpacing.xxl,
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(color: Colors.grey.shade300),
                  ),
                ),
                child: Row(
                  children: [
                    Flexible(
                      flex: 1,
                      child: Container(
                        constraints:
                            BoxConstraints(minWidth: AppSpacing.width180),
                        alignment: Alignment.center,
                        padding: EdgeInsets.only(left: 0),
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            SizedBox(
                              width: 20,
                            ),
                            Text(
                              'Modules',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontManager.semiBold,
                                color: Theme.of(context).brightness ==
                                        Brightness.dark
                                    ? AppColors.textPrimaryDark
                                    : Colors.black,
                                fontFamily: FontManager.fontFamilyInter,
                              ),
                            ),
                            SizedBox(width: AppSpacing.sm),
                            CompositedTransformTarget(
                              link: provider.modulesButtonLink,
                              child: MouseRegion(
                                cursor: SystemMouseCursors.click,
                                onEnter: (_) {
                                  provider.isModulesButtonHovering = true;
                                },
                                onExit: (_) {
                                  provider.isModulesButtonHovering = false;
                                },
                                child: GestureDetector(
                                  onTap: () {
                                    if (provider.showModulesPopup) {
                                      provider.hideModulesPopupMenu();
                                    } else {
                                      provider.showModulesPopupMenu(context);
                                    }
                                  },
                                  child: AnimatedContainer(
                                    duration: Duration(milliseconds: 120),
                                    key: provider.modulesButtonKey,
                                    margin: EdgeInsets.symmetric(horizontal: 0),
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 6),
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                          color:
                                              provider.isModulesButtonHovering
                                                  ? Color(0xFFBDBDBD)
                                                  : Colors.grey.shade900),
                                      borderRadius: BorderRadius.circular(4),
                                      color: provider.isModulesButtonHovering
                                          ? Color(0xFFF5F7FA)
                                          : Colors.white,
                                    ),
                                    child: GestureDetector(
                                      onTap: () {
                                        if (provider.showModulesPopup) {
                                          provider.hideModulesPopupMenu();
                                        } else {
                                          provider
                                              .showModulesPopupMenu(context);
                                        }
                                      },
                                      child: Row(
                                        children: [
                                          Icon(Icons.add,
                                              size: AppSpacing.md,
                                              color: Colors.grey.shade700),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Flexible(
                      flex: 1,
                      child: Container(
                        constraints:
                            BoxConstraints(minWidth: AppSpacing.width180),
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                      ),
                    ),
                    Flexible(
                      flex: 1,
                      child: Container(
                        constraints:
                            BoxConstraints(minWidth: AppSpacing.width180),
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                      ),
                    ),
                    Flexible(
                      flex: 1,
                      child: Container(
                        constraints:
                            BoxConstraints(minWidth: AppSpacing.width180),
                        alignment: Alignment.centerLeft,
                        padding: EdgeInsets.only(left: 10),
                        child: Text(
                          'Drag Your Solutions',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontManager.semiBold,
                            color:
                                Theme.of(context).brightness == Brightness.dark
                                    ? AppColors.textPrimaryDark
                                    : Colors.black,
                            fontFamily: FontManager.fontFamilyInter,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // Content row
              Expanded(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Middle panel - Modules
                    Flexible(
                      flex: 1,
                      child: Container(
                        key: provider.modulesColumnKey,
                        constraints:
                            BoxConstraints(minWidth: AppSpacing.width180),
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                        child: _buildModulesSection(provider),
                      ),
                    ),
                    // Right panel - Submodules
                    Flexible(
                      flex: 1,
                      child: Container(
                        constraints:
                            BoxConstraints(minWidth: AppSpacing.width180),
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                        child: _buildSubmodulesSection(provider),
                      ),
                    ),
                    // Empty panel on the right
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(color: Colors.grey.shade300),
                          ),
                          color: Colors.white,
                        ),
                      ),
                    ),
                    // Left panel - Solutions list
                    Flexible(
                      flex: 1,
                      child: Container(
                        constraints:
                            BoxConstraints(minWidth: AppSpacing.width180),
                        child: Stack(
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 0, horizontal: 0),
                              child: LayoutBuilder(
                                  builder: (context, constraints) {
                                // Calculate how many items we can fit based on available height
                                // Subtract pagination bar height and some padding
                                final paginationHeight = min(
                                    MediaQuery.of(context).size.height * 0.05,
                                    40.0);
                                final availableHeight = constraints.maxHeight -
                                    paginationHeight -
                                    20;

                                // Get screen width to determine display count and item sizing
                                final screenWidth =
                                    MediaQuery.of(context).size.width;

                                // Optimize for different screen sizes
                                double itemHeight;
                                int displayCount;

                                if (screenWidth <= 1366) {
                                  // For 1366px screens, use smaller item height to fit 12 items
                                  itemHeight = 35.0; // Reduced from 38.0
                                  displayCount = 12;
                                } else if (screenWidth <= 1560) {
                                  // For screens between 1366-1560px
                                  itemHeight = 35.0;
                                  displayCount = 12;
                                } else if (screenWidth >= 1920) {
                                  // For 1920px screens, show more items to fill the space better
                                  itemHeight = 38.0;
                                  final maxPossibleItems =
                                      (availableHeight / itemHeight).floor();
                                  displayCount = min(maxPossibleItems,
                                      provider.currentPageItems.length + 3);
                                  displayCount = max(
                                      12, displayCount); // At least 12 items
                                } else {
                                  // For screens between 1560 and 1920, scale accordingly
                                  itemHeight = 35.0;
                                  final maxPossibleItems =
                                      (availableHeight / itemHeight).floor();
                                  displayCount = maxPossibleItems;
                                  displayCount = max(12, displayCount);
                                }
                                return ListView.builder(
                                  padding: EdgeInsets.zero,
                                  // Ensure we display enough items to fill the space
                                  itemCount: displayCount,
                                  itemBuilder: (context, index) {
                                    final itemIndex =
                                        (provider.currentPage - 1) *
                                                provider.itemsPerPage +
                                            index +
                                            1;

                                    // Calculate dynamic vertical padding for larger screens
                                    // This helps distribute items evenly when fewer items are shown

                                    return Container(
                                      height: itemHeight,
                                      child: index <
                                              provider.currentPageItems.length
                                          ? _HoverSolutionItem(
                                              itemIndex: itemIndex,
                                              screenWidth: screenWidth,
                                            )
                                          : SizedBox(),
                                    );
                                  },
                                );
                              }),
                            ),
                            Positioned(
                              bottom: 0,
                              left: 0,
                              right: 0,
                              child: Container(
                                height: min(
                                    MediaQuery.of(context).size.height * 0.05,
                                    40), // Cap height at 40px
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    // Left chevron
                                    _PaginationChevronButton(isLeft: true),
                                    SizedBox(width: AppSpacing.xs),
                                    // Right chevron
                                    _PaginationChevronButton(isLeft: false),
                                    SizedBox(width: AppSpacing.xl),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildModulesSection(WebBookSolutionProvider provider) {
    if (provider.modules.isEmpty) {
      return Container(); // Return empty container when no modules exist
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (int i = 0; i < provider.modules.length; i++)
          _buildModuleItem(provider.modules[i], i, provider),
      ],
    );
  }

  Widget _buildModuleItem(
      ModuleItem module, int index, WebBookSolutionProvider provider) {
    return Builder(
      builder: (context) => MouseRegion(
        cursor: SystemMouseCursors.click,
        child: GestureDetector(
          onTap: () {
            provider.selectModule(index);
          },
          child: Container(
            color: Color(0xFFF8F9FA),
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                // Static dropdown arrow for visual consistency
                Icon(Icons.keyboard_arrow_down, size: AppSpacing.size18),
                SizedBox(width: AppSpacing.xs),
                Expanded(
                  child: Text(
                    module.name,
                    style: FontManager.getCustomStyle(
                      fontWeight: FontManager.medium,
                      fontSize: ResponsiveFontSizes.bodyLarge(context),
                      color: Theme.of(context).brightness == Brightness.dark
                          ? AppColors.textPrimaryDark
                          : AppColors.textPrimaryLight,
                      fontFamily: FontManager.fontFamilyInter,
                    ),
                  ),
                ),
                MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    key: provider.submoduleButtonKeys[index],
                    onTap: () {
                      // Select the module first to show its submodules in the third column
                      provider.selectModule(index);
                      provider.setActiveModulePopupIndex(index);
                      provider.showSubmodulePopup(index);
                    },
                    child: Icon(Icons.add, size: AppSpacing.size18),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSubmodulesSection(WebBookSolutionProvider provider) {
    if (provider.selectedModuleIndex == null ||
        provider.selectedModuleIndex! >= provider.modules.length) {
      return Container(); // Return empty container when no module is selected
    }

    final selectedModule = provider.modules[provider.selectedModuleIndex!];
    if (selectedModule.submodules.isEmpty) {
      return Container(); // Return empty container when no submodules exist
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (String submodule in selectedModule.submodules)
          _buildSubmoduleItem(submodule),
      ],
    );
  }

  Widget _buildSubmoduleItem(String submoduleName) {
    return Builder(
      builder: (context) => Container(
        color: Color(0xFFF8F9FA),
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        margin: EdgeInsets.only(bottom: 2),
        child: Row(
          children: [
            // Static dropdown arrow for visual consistency
            Icon(Icons.keyboard_arrow_down, size: AppSpacing.size18),
            SizedBox(width: AppSpacing.xs),
            Expanded(
              child: Text(
                submoduleName,
                style: FontManager.getCustomStyle(
                  fontWeight: FontManager.medium,
                  fontSize: ResponsiveFontSizes.bodyLarge(context),
                  color: Theme.of(context).brightness == Brightness.dark
                      ? AppColors.textPrimaryDark
                      : AppColors.textPrimaryLight,
                  fontFamily: FontManager.fontFamilyInter,
                ),
              ),
            ),
            // Add button for submodule (could be used for sub-submodules in future)
            MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTap: () {
                  // Future: Add sub-submodule functionality
                },
                child: Icon(Icons.add,
                    size: ResponsiveFontSizes.bodyLarge(context)),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _PaginationChevronButton extends StatelessWidget {
  final bool isLeft;
  const _PaginationChevronButton({this.isLeft = false});

  @override
  Widget build(BuildContext context) {
    return Consumer<WebBookSolutionProvider>(
      builder: (context, provider, child) {
        final isHovering = isLeft
            ? provider.isPaginationLeftChevronHovering
            : provider.isPaginationRightChevronHovering;

        final bool isDisabled = isLeft
            ? provider.currentPage <= 1
            : provider.currentPage >= provider.totalPages;

        return MouseRegion(
          cursor:
              isDisabled ? SystemMouseCursors.basic : SystemMouseCursors.click,
          onEnter: (_) {
            if (!isDisabled) {
              if (isLeft) {
                provider.isPaginationLeftChevronHovering = true;
              } else {
                provider.isPaginationRightChevronHovering = true;
              }
            }
          },
          onExit: (_) {
            if (!isDisabled) {
              if (isLeft) {
                provider.isPaginationLeftChevronHovering = false;
              } else {
                provider.isPaginationRightChevronHovering = false;
              }
            }
          },
          child: GestureDetector(
            onTap: isDisabled
                ? null
                : () {
                    if (isLeft) {
                      provider.previousPage();
                    } else {
                      provider.nextPage();
                    }
                  },
            child: AnimatedContainer(
              duration: Duration(milliseconds: 120),
              padding: EdgeInsets.all(4),
              child: Icon(
                isLeft ? Icons.chevron_left : Icons.chevron_right,
                size: ResponsiveFontSizes.headlineSmall(context),
                color: isDisabled ? Colors.grey.shade300 : Colors.black,
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Hover solution item widget that follows the consistent hover pattern
class _HoverSolutionItem extends StatefulWidget {
  final int itemIndex;
  final double screenWidth;

  const _HoverSolutionItem({
    required this.itemIndex,
    required this.screenWidth,
  });

  @override
  State<_HoverSolutionItem> createState() => _HoverSolutionItemState();
}

class _HoverSolutionItemState extends State<_HoverSolutionItem> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      cursor: SystemMouseCursors.click,
      child: AnimatedContainer(
        duration: Duration(milliseconds: 120),
        decoration: BoxDecoration(
          color: isHovered
              ? (Theme.of(context).brightness == Brightness.dark
                  ? AppColors.textPrimaryDark.withValues(alpha: 0.08)
                  : AppColors.textPrimaryLight.withValues(alpha: 0.08))
              : Colors.transparent,
        ),
        child: Row(
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Expanded(
                child: Text(
                  'Solutions-${widget.itemIndex.toString().padLeft(2, '0')}',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodyMedium(context),
                    fontWeight: FontManager.medium,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? AppColors.textPrimaryDark
                        : AppColors.textPrimaryLight,
                    fontFamily: FontManager.fontFamilyTiemposText,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
